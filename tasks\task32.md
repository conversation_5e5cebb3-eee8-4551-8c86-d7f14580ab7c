I want to design enemies system for this game.
We already have player that can use sword to hit rabbit and a bow that can hit rabbit.
Now, I want to design enemy system(s). Here, I will describe what different functionality i am planning:
There will be different enemies: that attack range and that attack close (mele). Some enemies might spawn skeletons or other smaller enemies. Probably more kinds - not defined yet. But all of them will probably have some health, some damage, will spawn some resources and add player xp when killed.
Enemies might be teritorial - they only attack if player is in their range - then they follow player. When player moves out of their range then they stop following player and wait for a few seconds and then return to their teritory.
We have one big map with colliders. But map is devided into "regions" - region is assigned to one of our tilemap layers. We can consider these regions as a teritory of enemy. If enemy is following player then he can move out of his teritory, but later has to return.
There will be some regions (regionXmanager) that spawn for example max 2 enemies of given type, they can move in range of their teritory (idle-move-idle-move... to random positions). When enemies are killed then we will need some timer to calculate when new enemy can be spawned - this will be handled by regionXmanager.
There will also be regions that for example spawn max 6 enemies and when they reach 6 enemies then 4 of them will become "Aggressive" and will attack either player or buildings built by player. This has to have priority to attack player and then closest building (here also - combat building first priority, other second priority) BUT we need to also make sure that enemy won't go for a very long way just to attack something based on priority when he is very close to other non-priority target - so we need to take into consideration priority but also somehow adjust it to the distance. Distance is not in straight line but distance the enemy would need to really walk to reach target.
Later we will also add combat buildings that player can build to attack enemies and walls that have more health and are more difficult to destroy (walls have least priority but also might be required to destroy it by enemy to be able to pass).
So as for now i can count following priotity list: player, combat building, other building, wall.
We would need to add tags to player end existing buildings.
There are probably aspects that i didn't consider.

Help me design this system. Read all required files, including player controller, and all you need and design what is missing, what needst to be add/created and whole infrastructure of how this system of enemies will work and interact on the current game state. Your task is to prepare a complete document that describes the system in detail so that .