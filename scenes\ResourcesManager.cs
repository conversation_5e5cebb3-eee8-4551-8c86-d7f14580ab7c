using Godot;
using System;
using System.Collections.Generic;
using System.Text.Json;

public partial class ResourcesManager : Node
{
	// Singleton instance
	public static ResourcesManager Instance { get; private set; }

	// Auto-save settings
	[Export] public float AutoSaveIntervalMinutes { get; set; } = 1.0f;
	[Export] public string DefaultSaveFileName { get; set; } = "autosave";
	[Export] public bool EnableAutoSave { get; set; } = true;

	// Events
	public event EventHandler<ResourceChangedEventArgs> ResourceChanged;
	public event EventHandler<StatChangedEventArgs> StatChanged;
	public event EventHandler GameSaved;
	public event EventHandler GameLoaded;

	// Private fields
	private Timer _autoSaveTimer;
	private DateTime _gameStartTime;
	public bool IsInitialized = false;
	private bool _alreadySavedOnExit = false;
	
	public override void _Ready()
	{
		if (Instance == null)
		{
			Instance = this;
			_gameStartTime = DateTime.Now;

			SetupAutoSaveTimer();
			LoadGame(DefaultSaveFileName);
			InitializeQuickSelectItems();
			InitializeDefaultToolLevels();

			IsInitialized = true;
			GD.Print("ResourcesManager initialized and ready");
		}
		else
		{
			QueueFree();
		}
	}

	public override void _Notification(int what)
	{
		//if(_alreadySavedOnExit) return;
		if (what == NotificationWMCloseRequest || what == NotificationApplicationPaused || what == NotificationApplicationFocusOut)
		{
			GD.Print("ResourcesManager: Application exit detected, saving game...");
			if (IsInitialized)
			{
				SaveGame(DefaultSaveFileName);
				_alreadySavedOnExit = true;
				GD.Print("ResourcesManager: Game saved on exit");
			}
		}
	}

	public override void _ExitTree()
	{
		//if(_alreadySavedOnExit) return;
		GD.Print("ResourcesManager: _ExitTree called");
		// Always save when exiting (regardless of auto-save setting)
		if (IsInitialized)
		{
			SaveGame(DefaultSaveFileName);
			_alreadySavedOnExit = true;
			GD.Print("ResourcesManager: Game saved in _ExitTree");
		}
	}

	private void SetupAutoSaveTimer()
	{
		_autoSaveTimer = new Timer();
		_autoSaveTimer.WaitTime = AutoSaveIntervalMinutes * 60.0f; // Convert minutes to seconds
		_autoSaveTimer.Autostart = EnableAutoSave;
		_autoSaveTimer.Timeout += OnAutoSaveTimeout;
		AddChild(_autoSaveTimer);
	}

	private void OnAutoSaveTimeout()
	{
		if (EnableAutoSave)
		{
			SaveGame(DefaultSaveFileName);
		}
	}

	public bool SaveGame(string filename = null)
	{
		filename ??= DefaultSaveFileName;

		// Update save metadata
		GD.Print(GameSaveData.Instance.WorldData.Chests.Count);
		GameSaveData.Instance.SaveTime = DateTime.Now;
		GameSaveData.Instance.PlayTimeSeconds += (int)(DateTime.Now - _gameStartTime).TotalSeconds;
		_gameStartTime = DateTime.Now; // Reset for next calculation

		var success = SaveHandler.Save(GameSaveData.Instance, filename);

		if (success)
		{
			GameSaved?.Invoke(this, EventArgs.Empty);
		}
		else
		{
			GD.PrintErr($"Failed to save game to {filename}");
		}

		return success;
	}

	public bool LoadGame(string filename = null)
	{
		filename ??= DefaultSaveFileName;

		var loadedData = SaveHandler.Load<GameSaveData>(filename);

		if (loadedData != null && loadedData.IsValid())
		{
			GameSaveData.LoadInstance(loadedData);
			_gameStartTime = DateTime.Now; // Reset play time tracking

			GameLoaded?.Invoke(this, EventArgs.Empty);
			return true;
		}
		else
		{
			GameSaveData.ResetInstance(); // Use default data
			return false;
		}
	}
	public void ForceSave(string filename = null)
	{
		SaveGame(filename);
	}

	public bool AddResource(ResourceType resourceType, int quantity = 1)
	{
		if (!CanAddResource(resourceType, quantity))
		{
			return false;
		}

		int oldQuantity = GameSaveData.Instance.PlayerResources.GetResourceQuantity(resourceType);
		GameSaveData.Instance.PlayerResources.AddResource(resourceType, quantity);
		int newQuantity = GameSaveData.Instance.PlayerResources.GetResourceQuantity(resourceType);

		ResourceChanged?.Invoke(this, new ResourceChangedEventArgs
		{
			ResourceType = resourceType,
			OldValue = oldQuantity,
			NewValue = newQuantity,
			ChangeAmount = quantity
		});

		UpdateQuickSelectResourceQuantities();
		return true;
	}

	public bool RemoveResource(ResourceType resourceType, int quantity = 1)
	{
		int oldQuantity = GameSaveData.Instance.PlayerResources.GetResourceQuantity(resourceType);
		bool success = GameSaveData.Instance.PlayerResources.RemoveResource(resourceType, quantity);

		if (success)
		{
			int newQuantity = GameSaveData.Instance.PlayerResources.GetResourceQuantity(resourceType);
			ResourceChanged?.Invoke(this, new ResourceChangedEventArgs
			{
				ResourceType = resourceType,
				OldValue = oldQuantity,
				NewValue = newQuantity,
				ChangeAmount = -quantity
			});

			UpdateQuickSelectResourceQuantities();
		}

		return success;
	}

	public bool HasResource(ResourceType resourceType, int quantity = 1)
	{
		return GameSaveData.Instance.PlayerResources.HasResource(resourceType, quantity);
	}

	public void EmitStatChanged(string statName, float newValue, float oldValue = 0)
	{
		StatChanged?.Invoke(this, new StatChangedEventArgs
		{
			StatName = statName,
			OldValue = oldValue,
			NewValue = newValue,
			ChangeAmount = newValue - oldValue
		});
	}

	public bool CanAddResource(ResourceType resourceType, int quantity = 1)
	{
		const int MAX_QUANTITY_PER_SLOT = 9999;
		int currentQuantity = GameSaveData.Instance.PlayerResources.GetResourceQuantity(resourceType);

		if (currentQuantity > 0)
		{
			return (currentQuantity + quantity) <= MAX_QUANTITY_PER_SLOT;
		}

		int maxSlots = GameSaveData.Instance.PlayerStats.MaxInventorySlots;
		int usedSlots = GameSaveData.Instance.PlayerResources.Resources.Count;

		return usedSlots < maxSlots;
	}

	private void InitializeDefaultToolLevels()
	{
		if (GameSaveData.Instance.PlayerStats.ToolLevels.Count == 0)
		{
			GameSaveData.Instance.PlayerStats.ToolLevels[ToolType.Pickaxe] = 1;
			GameSaveData.Instance.PlayerStats.ToolLevels[ToolType.Hammer] = 1;
			GameSaveData.Instance.PlayerStats.ToolLevels[ToolType.Hoe] = 1;
			GameSaveData.Instance.PlayerStats.ToolLevels[ToolType.WateringCan] = 1;
			GameSaveData.Instance.PlayerStats.ToolLevels[ToolType.Sword] = 1;
			GameSaveData.Instance.PlayerStats.ToolLevels[ToolType.Bow] = 1;
		}
	}

	public int GetToolLevel(ToolType toolType)
	{
		return GameSaveData.Instance.PlayerStats.ToolLevels.TryGetValue(toolType, out int level) ? level : 1;
	}

	public void SetToolLevel(ToolType toolType, int level)
	{
		int oldLevel = GetToolLevel(toolType);
		GameSaveData.Instance.PlayerStats.ToolLevels[toolType] = Math.Max(1, Math.Min(level, 10));
		int newLevel = GameSaveData.Instance.PlayerStats.ToolLevels[toolType];

		if (oldLevel != newLevel)
		{
			GD.Print($"ResourcesManager: {toolType} level changed from {oldLevel} to {newLevel}");
		}
	}

	public void SaveCustomLayerData(CustomDataLayerManager customDataManager)
	{
		if (customDataManager != null)
		{
			string jsonData = customDataManager.SaveToJson();
			// Parse the JSON string to an object to avoid double encoding
			try
			{
				var options = new JsonSerializerOptions
				{
					PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
					Converters = { new Vector2IJsonConverter() }
				};
				var jsonObject = JsonSerializer.Deserialize<object>(jsonData, options);
				GameSaveData.Instance.WorldData.CustomLayerData["tileData"] = jsonObject;
			}
			catch (Exception ex)
			{
				GD.PrintErr($"Failed to parse custom layer data JSON: {ex.Message}");
				// Fallback to storing as string (will cause double encoding but won't crash)
				GameSaveData.Instance.WorldData.CustomLayerData["tileData"] = jsonData;
			}

			// Save bridge tile data using simple serializable format
			try
			{
				var bridgeTiles = customDataManager.GetAllBridgeTiles();
				GameSaveData.Instance.WorldData.BridgeTiles.Clear();

				foreach (var kvp in bridgeTiles)
				{
					var pos = kvp.Key;
					var tileData = kvp.Value;
					GameSaveData.Instance.WorldData.BridgeTiles.Add(new BridgeTileData
					{
						X = pos.X,
						Y = pos.Y,
						SourceId = tileData.SourceId,
						AtlasX = tileData.AtlasCoords.X,
						AtlasY = tileData.AtlasCoords.Y
					});
				}
			}
			catch (Exception ex)
			{
				GD.PrintErr($"Failed to save bridge tile data: {ex.Message}");
				// Don't let bridge tile save failure break the entire save system
			}

			GameSaveData.Instance.WorldData.LastSaved = DateTime.Now;
		}
	}

	public void LoadCustomLayerData(CustomDataLayerManager customDataManager)
	{
		if (customDataManager != null && GameSaveData.Instance.WorldData.CustomLayerData.ContainsKey("tileData"))
		{
			var tileDataValue = GameSaveData.Instance.WorldData.CustomLayerData["tileData"];
			string jsonData;

			// Handle both old format (string) and new format (object)
			if (tileDataValue is string stringData)
			{
				// Old format: already a JSON string
				jsonData = stringData;
			}
			else
			{
				// New format: object that needs to be serialized to JSON
				try
				{
					var options = new JsonSerializerOptions
					{
						PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
						Converters = { new Vector2IJsonConverter() }
					};
					jsonData = JsonSerializer.Serialize(tileDataValue, options);
				}
				catch (Exception ex)
				{
					GD.PrintErr($"Failed to serialize custom layer data: {ex.Message}");
					return;
				}
			}

			customDataManager.LoadFromJson(jsonData);

			// Load bridge tile data using simple serializable format
			try
			{
				if (GameSaveData.Instance.WorldData.BridgeTiles.Count > 0)
				{
					var bridgeTiles = new Dictionary<Vector2I, CustomDataLayerManager.BridgeTileData>();

					foreach (var bridgeTileData in GameSaveData.Instance.WorldData.BridgeTiles)
					{
						var pos = new Vector2I(bridgeTileData.X, bridgeTileData.Y);
						var atlasCoords = new Vector2I(bridgeTileData.AtlasX, bridgeTileData.AtlasY);

						bridgeTiles[pos] = new CustomDataLayerManager.BridgeTileData
						{
							Position = pos,
							SourceId = bridgeTileData.SourceId,
							AtlasCoords = atlasCoords
						};
					}

					customDataManager.RestoreBridgeTiles(bridgeTiles);
				}
			}
			catch (Exception ex)
			{
				GD.PrintErr($"Failed to load bridge tile data: {ex.Message}");
				// Don't let bridge tile load failure break the entire load system
			}
		}
	}

	public int GetPlayTimeSeconds()
	{
		return GameSaveData.Instance.PlayTimeSeconds + (int)(DateTime.Now - _gameStartTime).TotalSeconds;
	}

	public string AddBuilding(Vector2I topLeftTile, string buildingType, int buildingId)
	{
		var buildingData = new BuildingData
		{
			TopLeftX = topLeftTile.X,
			TopLeftY = topLeftTile.Y,
			BuildingType = buildingType,
			BuildingId = buildingId
		};
		GameSaveData.Instance.WorldData.Buildings.Add(buildingData);
		return buildingData.Id;
	}

	public bool RemoveBuildingById(string id)
	{
		int removedCount = GameSaveData.Instance.WorldData.Buildings.RemoveAll(b => b.Id == id);
		return removedCount > 0;
	}

	public List<BuildingData> GetBuildings()
	{
		return GameSaveData.Instance.WorldData.Buildings;
	}
	public void ClearBuildings()
	{
		GameSaveData.Instance.WorldData.Buildings.Clear();
	}

	public void InitializeQuickSelectItems()
	{
		if (GameSaveData.Instance.PlayerStats.QuickSelectItems.Count == 0)
		{
			for (int i = 0; i < 10; i++)
			{
				GameSaveData.Instance.PlayerStats.QuickSelectItems.Add(new QuickSelectItem());
			}

			SetQuickSelectTool(0, ToolType.Pickaxe);    // Slot 1 (index 0)
			SetQuickSelectTool(1, ToolType.Hoe);        // Slot 2 (index 1)
			SetQuickSelectTool(2, ToolType.WateringCan); // Slot 3 (index 2)
			SetQuickSelectTool(3, ToolType.Hammer);     // Slot 4 (index 3)
			SetQuickSelectTool(4, ToolType.Sword);      // Slot 5 (index 4)
			SetQuickSelectTool(5, ToolType.Bow);        // Slot 6 (index 5)
		}
	}

	public void SetQuickSelectTool(int slotIndex, ToolType toolType)
	{
		if (slotIndex < 0 || slotIndex >= 10) return;

		EnsureQuickSelectSlots();
		var item = GameSaveData.Instance.PlayerStats.QuickSelectItems[slotIndex];
		item.IsEmpty = false;
		item.IsTool = true;
		item.ToolType = toolType;
		item.ResourceType = ResourceType.None; // Default, not used for tools
		item.Quantity = 0; // Not used for tools
	}

	public void SetQuickSelectResource(int slotIndex, ResourceType resourceType)
	{
		if (slotIndex < 0 || slotIndex >= 10) return;

		EnsureQuickSelectSlots();
		var item = GameSaveData.Instance.PlayerStats.QuickSelectItems[slotIndex];
		item.IsEmpty = false;
		item.IsTool = false;
		item.ToolType = ToolType.None; // Not used for resources
		item.ResourceType = resourceType;
		item.Quantity = GameSaveData.Instance.PlayerResources.GetResourceQuantity(resourceType);
	}

	public void ClearQuickSelectSlot(int slotIndex)
	{
		if (slotIndex < 0 || slotIndex >= 10) return;

		EnsureQuickSelectSlots();
		var item = GameSaveData.Instance.PlayerStats.QuickSelectItems[slotIndex];
		item.IsEmpty = true;
		item.IsTool = false;
		item.ToolType = ToolType.None;
		item.ResourceType = ResourceType.Wood;
		item.Quantity = 0;
	}

	public QuickSelectItem GetQuickSelectItem(int slotIndex)
	{
		if (slotIndex < 0 || slotIndex >= 10) return new QuickSelectItem();

		EnsureQuickSelectSlots();
		return GameSaveData.Instance.PlayerStats.QuickSelectItems[slotIndex];
	}

	public void UpdateQuickSelectResourceQuantities()
	{
		EnsureQuickSelectSlots();
		for (int i = 0; i < GameSaveData.Instance.PlayerStats.QuickSelectItems.Count; i++)
		{
			var item = GameSaveData.Instance.PlayerStats.QuickSelectItems[i];
			if (!item.IsEmpty && !item.IsTool)
			{
				int currentQuantity = GameSaveData.Instance.PlayerResources.GetResourceQuantity(item.ResourceType);
				item.Quantity = currentQuantity;

				// Remove item if quantity is 0
				if (currentQuantity == 0)
				{
					ClearQuickSelectSlot(i);
				}
			}
		}
	}

	private void EnsureQuickSelectSlots()
	{
		while (GameSaveData.Instance.PlayerStats.QuickSelectItems.Count < 10)
		{
			GameSaveData.Instance.PlayerStats.QuickSelectItems.Add(new QuickSelectItem());
		}
	}

	public List<RabbitSaveData> LoadRabbitData(int regionId)
	{
		string key = $"rabbits_region_{regionId}";

		if (GameSaveData.Instance.WorldData.CustomLayerData.TryGetValue(key, out var data))
		{
			try
			{
				// Handle both JsonElement and direct List<RabbitSaveData> cases
				if (data is JsonElement jsonElement)
				{
					var options = new JsonSerializerOptions
					{
						PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
						Converters = { new Vector2JsonConverter() }
					};
					var rabbitDataList = JsonSerializer.Deserialize<List<RabbitSaveData>>(jsonElement.GetRawText(), options);
					GD.Print($"ResourcesManager: Loaded {rabbitDataList?.Count ?? 0} rabbits for region {regionId}");
					return rabbitDataList ?? new List<RabbitSaveData>();
				}
				else if (data is List<RabbitSaveData> directList)
				{
					GD.Print($"ResourcesManager: Loaded {directList.Count} rabbits for region {regionId} (direct)");
					return directList;
				}
			}
			catch (Exception ex)
			{
				GD.PrintErr($"ResourcesManager: Failed to deserialize rabbit data for region {regionId}: {ex.Message}");
			}
		}

		GD.Print($"ResourcesManager: No rabbit data found for region {regionId}");
		return new List<RabbitSaveData>();
	}

	/// <summary>
	/// Load enemy data for a specific region
	/// </summary>
	public List<EnemySaveData> LoadEnemyData(int regionId)
	{
		string key = $"enemies_region_{regionId}";

		if (GameSaveData.Instance.WorldData.CustomLayerData.TryGetValue(key, out var data))
		{
			try
			{
				// Handle both JsonElement and direct List<EnemySaveData> cases
				if (data is JsonElement jsonElement)
				{
					var options = new JsonSerializerOptions
					{
						PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
						Converters = { new Vector2JsonConverter() }
					};
					var enemyDataList = JsonSerializer.Deserialize<List<EnemySaveData>>(jsonElement.GetRawText(), options);
					GD.Print($"ResourcesManager: Loaded {enemyDataList?.Count ?? 0} enemies for region {regionId}");
					return enemyDataList ?? new List<EnemySaveData>();
				}
				else if (data is List<EnemySaveData> directList)
				{
					GD.Print($"ResourcesManager: Loaded {directList.Count} enemies for region {regionId} (direct)");
					return directList;
				}
			}
			catch (Exception ex)
			{
				GD.PrintErr($"ResourcesManager: Failed to deserialize enemy data for region {regionId}: {ex.Message}");
			}
		}

		GD.Print($"ResourcesManager: No enemy data found for region {regionId}");
		return new List<EnemySaveData>();
	}

	public Dictionary<Vector2I, int> LoadObjectHealthData(int regionId)
	{
		string key = $"object_health_region_{regionId}";

		if (GameSaveData.Instance.WorldData.CustomLayerData.TryGetValue(key, out var data))
		{
			try
			{
				// Handle both JsonElement and direct Dictionary cases
				if (data is JsonElement jsonElement)
				{
					var options = new JsonSerializerOptions
					{
						PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
						Converters = { new Vector2IJsonConverter() }
					};
					var healthData = JsonSerializer.Deserialize<Dictionary<Vector2I, int>>(jsonElement.GetRawText(), options);
					GD.Print($"ResourcesManager: Loaded health data for {healthData?.Count ?? 0} objects in region {regionId}");
					return healthData ?? new Dictionary<Vector2I, int>();
				}
				else if (data is Dictionary<Vector2I, int> directDict)
				{
					GD.Print($"ResourcesManager: Loaded health data for {directDict.Count} objects in region {regionId} (direct)");
					return directDict;
				}
			}
			catch (Exception ex)
			{
				GD.PrintErr($"ResourcesManager: Failed to deserialize object health data for region {regionId}: {ex.Message}");
			}
		}

		GD.Print($"ResourcesManager: No object health data found for region {regionId}");
		return new Dictionary<Vector2I, int>();
	}
}
