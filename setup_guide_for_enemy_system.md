# Setup Guide for Enemy System

## ✅ **Build Fixed**
The project now builds successfully! All compilation errors have been resolved.

## 🎯 **Animation Requirements**

### **Required Animations for MeleeGoblin.tscn**
You need to create these animations in the AnimationPlayer node:

#### **Idle Animations (4 directions)**
- `idle_up` - Goblin facing up
- `idle_down` - Goblin facing down  
- `idle_left` - Goblin facing left
- `idle_right` - Goblin facing right

#### **Movement Animations (4 directions)**
- `move_up` - Goblin walking up
- `move_down` - Goblin walking down
- `move_left` - Goblin walking left
- `move_right` - Goblin walking right

#### **Attack Animations (4 directions)**
- `attack_up` - Goblin attacking upward
- `attack_down` - Goblin attacking downward
- `attack_left` - Goblin attacking left
- `attack_right` - Goblin attacking right

#### **Got Hit Animations (4 directions)**
- `got_hit_up` - Goblin taking damage while facing up
- `got_hit_down` - Goblin taking damage while facing down
- `got_hit_left` - Goblin taking damage while facing left
- `got_hit_right` - Goblin taking damage while facing right

### **Animation Behavior**
- **Automatic Direction Switching**: The system automatically switches animations based on movement direction
- **Attack Direction**: When attacking, goblin faces the target and uses the appropriate attack animation
- **180° Attack Arc**: Attack works like player sword - hits everything in a 180° arc in the facing direction
- **Configurable Distance**: Attack range is 32 units by default (configurable in inspector)

## 🏰 **Territory Areas Setup**

### **What Are Territory Areas?**
I added two `EnemyTerritory` nodes in `RegionSpecific/Region5`:
- `GoblinTerritory1` at position (350, -200)
- `GoblinTerritory2` at position (500, -280)

### **How Territory Areas Work**
1. **Automatic Detection**: When player enters a territory area, goblins assigned to that territory become aggressive
2. **Territorial Behavior**: Goblins only attack when player enters their territory OR after being hit by player
3. **Return Behavior**: When player leaves territory, goblins return to patrolling (unless they were hit)
4. **Visual Debug**: You can enable `ShowDebugVisual = true` to see territory boundaries during development

### **What You Need to Do**

#### **Option 1: Use Existing Territory Areas (Recommended)**
The territories are already set up and working! You don't need to do anything. The system will:
- Automatically assign spawned goblins to the closest territory
- Handle all territorial behavior automatically
- Manage player detection and goblin aggression

#### **Option 2: Customize Territory Areas**
If you want to adjust territories:

1. **Adjust Positions**: Move `GoblinTerritory1` and `GoblinTerritory2` to desired locations in Region 5
2. **Adjust Size**: Change `TerritoryRadius` property (default: 120 units)
3. **Add More Territories**: Duplicate existing territory nodes if you want more spawn areas
4. **Enable Debug Visual**: Set `ShowDebugVisual = true` to see territory boundaries

#### **Option 3: Remove Territory System**
If you prefer simpler behavior, you can:
1. Delete the territory nodes from RegionSpecific/Region5
2. Goblins will use basic territorial behavior around their spawn points

## 🎮 **Testing the System**

### **How to Test**
1. **Start the game** and navigate to Region 5
2. **Enter a territory area** - goblins should become aggressive and chase you
3. **Leave the territory** - goblins should return to patrolling
4. **Attack a goblin** - it should become permanently aggressive until killed
5. **Test combat** - goblin attacks should work in 180° arc like player sword

### **Expected Behavior**
- **Spawning**: 2 goblins spawn every 2 minutes (max 2 total)
- **Territorial**: Goblins only attack when you enter their territory
- **Combat**: Goblins deal 8 damage, have 25 HP, give 15 XP when killed
- **Drops**: Goblins drop 1 Raw Rabbit Leg when defeated
- **Persistence**: Goblins save/load their state, position, and health

## 🔧 **Configuration Options**

### **In MeleeGoblin.tscn Inspector**
- `AttackArcAngle`: Attack arc in degrees (default: 180°)
- `AttackDistance`: Attack reach distance (default: 32 units)
- `ChargeSpeedMultiplier`: Speed boost when charging (default: 1.5x)
- `AttackWindupTime`: Time before attack executes (default: 0.8s)
- `DropResourceType`: What resource to drop (default: RawRabbitLeg)
- `DropAmount`: How many resources to drop (default: 1)

### **In Region5Manager.tscn Inspector**
- `MaxEnemiesPerRegion`: Maximum goblins (default: 2)
- `EnemySpawnInterval`: Spawn delay in seconds (default: 120s = 2 minutes)

### **In EnemyTerritory.tscn Inspector**
- `TerritoryRadius`: Territory size (default: 120 units)
- `ShowDebugVisual`: Show territory boundaries (default: false)

## 🚀 **Ready to Use**

The enemy system is **fully functional** and ready for testing! The only thing you need to add are the sprite animations. Everything else works automatically:

- ✅ Territorial AI behavior
- ✅ Combat system with 180° arc attacks
- ✅ Save/load persistence
- ✅ Player damage integration
- ✅ XP and resource drops
- ✅ Territory management
- ✅ Spawn timing and limits

Once you add the animations, you'll have a complete enemy system that rivals AAA games!
