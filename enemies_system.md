# Enemy System Technical Design Document

## Executive Summary

This document outlines a comprehensive enemy system for the Godot-based crafting game, designed to integrate seamlessly with existing systems including region management, combat mechanics, pathfinding, and save/load functionality. The system emphasizes territorial AI, region-based spawning, and AAA-quality enemy behaviors.

## Current System Analysis

### Existing Combat Infrastructure
- **Player Combat**: Sword (melee, arc-based damage) and Bow (ranged projectiles)
- **Damage System**: Signal-based with `CommonSignals.SwordUsed` and arrow collision detection
- **Health/Damage**: Standardized `TakeDamage(int damage)` interface via `IDestroyableObject`
- **Knockback**: Tween-based physics with collision detection
- **Animation**: AnimationPlayer with directional animations (idle/move/attack patterns)

### Region Management Architecture
- **RegionXManager Classes**: Handle spawning, save/load, and object lifecycle
- **Territorial Boundaries**: Region-based tile system with `CustomDataLayerManager`
- **Spawn Limits**: Configurable per region (currently 2 rabbits max in passive regions)
- **Save Integration**: Direct GameData updates with region-specific keys

### Pathfinding & Movement
- **Basic Pathfinding**: Ray-casting collision detection (`IsPathClear`)
- **Movement System**: Lerp-based with start/target positions and progress tracking
- **Collision Layers**: Established layer system for different object types
- **Tile-Based Logic**: 16x16 tile grid with position calculations

## System Architecture

### Core Enemy Classes

#### 1. Base Enemy Class
```csharp
public abstract partial class BaseEnemy : CharacterBody2D, IDestroyableObject
{
    // Core Properties
    [Export] public EnemyType EnemyType { get; set; }
    [Export] public int MaxHealth { get; set; } = 20;
    [Export] public int AttackDamage { get; set; } = 5;
    [Export] public float MovementSpeed { get; set; } = 30.0f;
    [Export] public float DetectionRange { get; set; } = 80.0f;
    [Export] public float AttackRange { get; set; } = 24.0f;
    [Export] public float TerritoryRadius { get; set; } = 120.0f;
    [Export] public int XpReward { get; set; } = 15;
    
    // Territory & AI
    protected Vector2 _territoryCenter;
    protected EnemyState _currentState = EnemyState.Patrolling;
    protected Node2D _currentTarget;
    protected float _lastAttackTime = 0.0f;
    protected float _attackCooldown = 2.0f;
    
    // Movement & Pathfinding
    protected Vector2 _startPosition;
    protected Vector2 _targetPosition;
    protected float _movementProgress = 0.0f;
    protected List<Vector2> _currentPath = new();
    protected int _currentPathIndex = 0;
}
```

#### 2. Enemy State Machine
```csharp
public enum EnemyState
{
    Patrolling,     // Random movement within territory
    Pursuing,       // Chasing target
    Attacking,      // In combat range
    Returning,      // Returning to territory
    Stunned,        // Temporary disable after damage
    Dead
}
```

#### 3. Enemy Types

**Melee Enemy (Goblin)**
- Close-range attacker with sword/club
- High health, moderate damage
- Charges at targets when in range

**Ranged Enemy (Archer)**
- Shoots projectiles at distance
- Lower health, moderate damage
- Maintains distance from targets

**Summoner Enemy (Necromancer)**
- Spawns skeleton minions
- High health, low direct damage
- Focuses on summoning rather than direct combat

### Advanced AI System

#### 1. Territorial Behavior
```csharp
public class TerritorialAI
{
    private Vector2 _territoryCenter;
    private float _territoryRadius;
    private float _returnThreshold;
    
    public bool IsInTerritory(Vector2 position)
    {
        return _territoryCenter.DistanceTo(position) <= _territoryRadius;
    }
    
    public bool ShouldReturnToTerritory(Vector2 currentPosition, Node2D target)
    {
        if (target == null) return true;
        
        float distanceFromTerritory = _territoryCenter.DistanceTo(currentPosition);
        float targetDistance = currentPosition.DistanceTo(target.GlobalPosition);
        
        // Return if too far from territory and target is also far
        return distanceFromTerritory > _territoryRadius && targetDistance > _returnThreshold;
    }
}
```

#### 2. Advanced Pathfinding System
```csharp
public class EnemyPathfinding
{
    private const int MAX_PATH_NODES = 50;
    private const float NODE_SPACING = 16.0f;
    
    public List<Vector2> FindPath(Vector2 start, Vector2 end, float maxDistance = 200.0f)
    {
        // A* pathfinding implementation
        // Considers building collision, terrain, and other enemies
        // Returns optimized path with waypoints
    }
    
    public Vector2 GetNextPatrolPoint(Vector2 currentPos, Vector2 territoryCenter, float radius)
    {
        // Generate smart patrol points within territory
        // Avoids clustering, ensures coverage
    }
}
```

#### 3. Target Priority System
```csharp
public class TargetPrioritySystem
{
    public enum TargetType
    {
        Player = 100,
        CombatBuilding = 80,
        ProductionBuilding = 60,
        Wall = 40,
        None = 0
    }
    
    public Node2D SelectBestTarget(List<Node2D> potentialTargets, Vector2 enemyPosition)
    {
        var scoredTargets = potentialTargets
            .Select(target => new {
                Target = target,
                Score = CalculateTargetScore(target, enemyPosition)
            })
            .OrderByDescending(x => x.Score)
            .ToList();
            
        return scoredTargets.FirstOrDefault()?.Target;
    }
    
    private float CalculateTargetScore(Node2D target, Vector2 enemyPosition)
    {
        float priorityScore = GetTargetPriority(target);
        float distance = enemyPosition.DistanceTo(target.GlobalPosition);
        float pathDistance = GetPathfindingDistance(enemyPosition, target.GlobalPosition);
        
        // Balance priority vs distance (closer targets get bonus)
        float distanceModifier = Mathf.Max(0.1f, 1.0f - (pathDistance / 300.0f));
        return priorityScore * distanceModifier;
    }
}
```

### Region-Based Spawning System

#### 1. Enhanced Region Manager Integration
```csharp
public partial class EnemySpawnManager : Node2D
{
    [Export] public int MaxPassiveEnemies { get; set; } = 2;
    [Export] public int MaxAggressiveEnemies { get; set; } = 6;
    [Export] public float SpawnInterval { get; set; } = 30.0f;
    [Export] public bool IsAggressiveRegion { get; set; } = false;
    
    private List<BaseEnemy> _activeEnemies = new();
    private Timer _spawnTimer;
    private Random _random = new();
    
    public void TrySpawnEnemy()
    {
        int maxEnemies = IsAggressiveRegion ? MaxAggressiveEnemies : MaxPassiveEnemies;
        if (_activeEnemies.Count >= maxEnemies) return;
        
        var spawnPoint = GetValidSpawnPoint();
        if (spawnPoint != Vector2.Zero)
        {
            SpawnEnemyAt(spawnPoint, SelectEnemyType());
        }
    }
    
    private void ManageAggressiveBehavior()
    {
        if (!IsAggressiveRegion || _activeEnemies.Count < 4) return;
        
        // Make 4 enemies aggressive when cap is reached
        var passiveEnemies = _activeEnemies.Where(e => e.GetCurrentState() == EnemyState.Patrolling).Take(4);
        foreach (var enemy in passiveEnemies)
        {
            enemy.SetAggressive(true);
        }
    }
}
```

#### 2. Save/Load Integration
```csharp
[System.Serializable]
public class EnemySaveData
{
    public Vector2 Position { get; set; }
    public int Health { get; set; }
    public int AssignedRegion { get; set; }
    public EnemyType EnemyType { get; set; }
    public EnemyState CurrentState { get; set; }
    public Vector2 TerritoryCenter { get; set; }
    public bool IsAggressive { get; set; }
    public float LastAttackTime { get; set; }
}
```

### Combat Integration

#### 1. Enhanced CommonSignals
```csharp
// Add to CommonSignals.cs
[Signal]
public delegate void EnemyAttackEventHandler(Vector2 attackPosition, int damage, EnemyType enemyType);

[Signal]
public delegate void EnemyDefeatedEventHandler(EnemyType enemyType, Vector2 position, int xpReward);

[Signal]
public delegate void EnemySpawnedEventHandler(EnemyType enemyType, Vector2 position, int regionId);
```

#### 2. Building Tagging System
```csharp
public interface ICombatTarget
{
    TargetType GetTargetType();
    bool CanBeTargeted();
    Vector2 GetTargetPosition();
    void OnTargeted(BaseEnemy enemy);
    void OnAttacked(int damage, EnemyType attackerType);
}

// Implement on existing buildings
public partial class Anvil : Node2D, IDestroyableObject, ICombatTarget
{
    public TargetType GetTargetType() => TargetType.ProductionBuilding;
    public bool CanBeTargeted() => _isPlaced && !_isBeingDestroyed;
    // ... other implementations
}
```

### Performance Optimization

#### 1. Enemy Manager
```csharp
public class EnemyManager : Node
{
    private static EnemyManager _instance;
    private List<BaseEnemy> _allEnemies = new();
    private Dictionary<int, List<BaseEnemy>> _enemiesByRegion = new();
    
    // Update only nearby enemies based on player position
    public void UpdateNearbyEnemies(Vector2 playerPosition, float updateRadius = 200.0f)
    {
        var nearbyEnemies = _allEnemies.Where(e => 
            e.GlobalPosition.DistanceTo(playerPosition) <= updateRadius).ToList();
            
        foreach (var enemy in nearbyEnemies)
        {
            enemy.SetUpdateEnabled(true);
        }
        
        // Disable distant enemies
        var distantEnemies = _allEnemies.Except(nearbyEnemies);
        foreach (var enemy in distantEnemies)
        {
            enemy.SetUpdateEnabled(false);
        }
    }
}
```

#### 2. Spatial Partitioning
```csharp
public class SpatialGrid
{
    private Dictionary<Vector2I, List<BaseEnemy>> _grid = new();
    private const int CELL_SIZE = 64;
    
    public List<BaseEnemy> GetNearbyEnemies(Vector2 position, float radius)
    {
        // Return enemies in nearby grid cells
        // Reduces collision checks and target searches
    }
}
```

## Implementation Phases

### Phase 1: Core Infrastructure (Week 1-2)
1. Create BaseEnemy class with basic state machine
2. Implement EnemySpawnManager integration with RegionXManager
3. Add enemy-specific CommonSignals
4. Create basic pathfinding system
5. Implement save/load for enemy data

### Phase 2: Basic Enemy Types (Week 3-4)
1. Implement MeleeEnemy (Goblin) with sword attacks
2. Create RangedEnemy (Archer) with projectile system
3. Add territorial AI and patrol behaviors
4. Integrate with existing combat system
5. Implement basic target priority system

### Phase 3: Advanced Features (Week 5-6)
1. Create SummonerEnemy with minion spawning
2. Implement advanced pathfinding with A*
3. Add building tagging system
4. Create aggressive region behaviors
5. Implement performance optimizations

### Phase 4: Polish & Balance (Week 7-8)
1. Fine-tune AI behaviors and difficulty
2. Add visual effects and animations
3. Implement sound integration
4. Performance testing and optimization
5. Integration testing with existing systems

## Technical Considerations

### Error Handling
- Null reference checks for all target acquisitions
- Graceful degradation when pathfinding fails
- Recovery mechanisms for stuck enemies
- Save data validation and migration

### Performance Guidelines
- Maximum 20 active enemies per region
- Update frequency scaling based on distance to player
- Efficient collision detection using spatial partitioning
- Memory pooling for frequently created objects (projectiles, effects)

### Integration Points
- Respect existing collision layers and masks
- Use established signal patterns for loose coupling
- Follow existing save/load architecture
- Maintain compatibility with building placement system

This design provides a solid foundation for a comprehensive enemy system that integrates seamlessly with your existing game architecture while providing AAA-quality AI behaviors and performance optimization.

## Detailed Implementation Specifications

### Enemy Behavior State Machines

#### Melee Enemy (Goblin) State Machine
```csharp
public partial class MeleeEnemy : BaseEnemy
{
    private const float CHARGE_SPEED_MULTIPLIER = 1.5f;
    private const float ATTACK_WINDUP_TIME = 0.8f;

    protected override void UpdateState(double delta)
    {
        switch (_currentState)
        {
            case EnemyState.Patrolling:
                HandlePatrolling(delta);
                CheckForTargets();
                break;

            case EnemyState.Pursuing:
                HandlePursuit(delta);
                CheckAttackRange();
                CheckTerritoryBounds();
                break;

            case EnemyState.Attacking:
                HandleMeleeAttack(delta);
                break;

            case EnemyState.Returning:
                HandleReturnToTerritory(delta);
                break;
        }
    }

    private void HandleMeleeAttack(double delta)
    {
        if (_currentTarget == null)
        {
            ChangeState(EnemyState.Patrolling);
            return;
        }

        // Face target during attack
        Vector2 directionToTarget = (_currentTarget.GlobalPosition - GlobalPosition).Normalized();
        UpdateFacingDirection(directionToTarget);

        // Execute attack after windup
        if (Time.GetTicksMsec() / 1000.0f - _attackStartTime >= ATTACK_WINDUP_TIME)
        {
            ExecuteMeleeAttack();
            _lastAttackTime = Time.GetTicksMsec() / 1000.0f;
            ChangeState(EnemyState.Pursuing);
        }
    }

    private void ExecuteMeleeAttack()
    {
        // Create attack area similar to player sword
        var attackArea = new Area2D();
        var attackShape = new CollisionShape2D();
        var circleShape = new CircleShape2D { Radius = AttackRange };

        attackShape.Shape = circleShape;
        attackArea.AddChild(attackShape);
        attackArea.GlobalPosition = GlobalPosition;

        GetParent().AddChild(attackArea);

        // Check for targets in attack range
        var bodies = attackArea.GetOverlappingBodies();
        foreach (var body in bodies)
        {
            if (body is PlayerController player)
            {
                // Apply damage and knockback to player
                ApplyAttackToTarget(player, AttackDamage);
            }
            else if (body is Node2D building && building.HasMethod("TakeDamage"))
            {
                building.Call("TakeDamage", AttackDamage);
            }
        }

        // Cleanup attack area
        attackArea.QueueFree();

        // Play attack animation and sound
        PlayAttackAnimation();
        PlayAttackSound();
    }
}
```

#### Ranged Enemy (Archer) Implementation
```csharp
public partial class RangedEnemy : BaseEnemy
{
    [Export] public PackedScene ProjectileScene { get; set; }
    [Export] public float ProjectileSpeed { get; set; } = 150.0f;
    [Export] public float OptimalRange { get; set; } = 100.0f;
    [Export] public float MinRange { get; set; } = 50.0f;

    private void HandleRangedAttack(double delta)
    {
        if (_currentTarget == null)
        {
            ChangeState(EnemyState.Patrolling);
            return;
        }

        float distanceToTarget = GlobalPosition.DistanceTo(_currentTarget.GlobalPosition);

        // Maintain optimal distance
        if (distanceToTarget < MinRange)
        {
            // Move away from target
            Vector2 retreatDirection = (GlobalPosition - _currentTarget.GlobalPosition).Normalized();
            MoveInDirection(retreatDirection, MovementSpeed * 0.8f, delta);
        }
        else if (distanceToTarget > AttackRange)
        {
            ChangeState(EnemyState.Pursuing);
            return;
        }
        else
        {
            // In optimal range - shoot
            if (CanAttack())
            {
                ShootProjectile();
                _lastAttackTime = Time.GetTicksMsec() / 1000.0f;
            }
        }
    }

    private void ShootProjectile()
    {
        if (ProjectileScene == null || _currentTarget == null) return;

        var projectile = ProjectileScene.Instantiate<EnemyProjectile>();
        Vector2 direction = (_currentTarget.GlobalPosition - GlobalPosition).Normalized();

        // Lead target based on movement
        if (_currentTarget is CharacterBody2D movingTarget)
        {
            Vector2 predictedPosition = PredictTargetPosition(movingTarget, direction);
            direction = (predictedPosition - GlobalPosition).Normalized();
        }

        projectile.GlobalPosition = GlobalPosition + direction * 16;
        projectile.Initialize(direction, ProjectileSpeed, AttackDamage);

        GetParent().AddChild(projectile);
        PlayShootAnimation();
        PlayShootSound();
    }

    private Vector2 PredictTargetPosition(CharacterBody2D target, Vector2 currentDirection)
    {
        float timeToTarget = GlobalPosition.DistanceTo(target.GlobalPosition) / ProjectileSpeed;
        return target.GlobalPosition + target.Velocity * timeToTarget;
    }
}
```

#### Summoner Enemy (Necromancer) System
```csharp
public partial class SummonerEnemy : BaseEnemy
{
    [Export] public PackedScene MinionScene { get; set; }
    [Export] public int MaxMinions { get; set; } = 3;
    [Export] public float SummonCooldown { get; set; } = 8.0f;
    [Export] public float SummonRange { get; set; } = 60.0f;

    private List<BaseEnemy> _activeMinions = new();
    private float _lastSummonTime = 0.0f;

    private void HandleSummoning(double delta)
    {
        // Clean up dead minions
        _activeMinions.RemoveAll(minion => !IsInstanceValid(minion));

        if (_currentTarget == null)
        {
            ChangeState(EnemyState.Patrolling);
            return;
        }

        // Maintain distance while summoning
        float distanceToTarget = GlobalPosition.DistanceTo(_currentTarget.GlobalPosition);
        if (distanceToTarget < SummonRange)
        {
            Vector2 retreatDirection = (GlobalPosition - _currentTarget.GlobalPosition).Normalized();
            MoveInDirection(retreatDirection, MovementSpeed * 0.6f, delta);
        }

        // Summon minions if possible
        if (CanSummon())
        {
            SummonMinion();
            _lastSummonTime = Time.GetTicksMsec() / 1000.0f;
        }
    }

    private bool CanSummon()
    {
        float currentTime = Time.GetTicksMsec() / 1000.0f;
        return _activeMinions.Count < MaxMinions &&
               (currentTime - _lastSummonTime) >= SummonCooldown;
    }

    private void SummonMinion()
    {
        if (MinionScene == null) return;

        Vector2 summonPosition = FindSummonPosition();
        if (summonPosition == Vector2.Zero) return;

        var minion = MinionScene.Instantiate<BaseEnemy>();
        minion.GlobalPosition = summonPosition;
        minion.SetTerritory(_territoryCenter, TerritoryRadius);
        minion.SetTarget(_currentTarget);

        GetParent().AddChild(minion);
        _activeMinions.Add(minion);

        // Visual effects
        PlaySummonAnimation();
        PlaySummonSound();
        CreateSummonEffect(summonPosition);
    }

    private Vector2 FindSummonPosition()
    {
        for (int attempts = 0; attempts < 8; attempts++)
        {
            float angle = (float)(GD.Randi() % 360) * Mathf.Pi / 180.0f;
            Vector2 offset = new Vector2(Mathf.Cos(angle), Mathf.Sin(angle)) * 40.0f;
            Vector2 testPosition = GlobalPosition + offset;

            if (IsPositionValid(testPosition))
            {
                return testPosition;
            }
        }
        return Vector2.Zero;
    }
}
```

### Advanced Pathfinding Implementation

#### A* Pathfinding System
```csharp
public class AStarPathfinding
{
    private class PathNode
    {
        public Vector2 Position;
        public float GCost; // Distance from start
        public float HCost; // Distance to target
        public float FCost => GCost + HCost;
        public PathNode Parent;
        public bool IsWalkable = true;
    }

    private const int GRID_SIZE = 16;
    private Dictionary<Vector2I, PathNode> _nodeGrid = new();
    private CustomDataLayerManager _customDataManager;

    public List<Vector2> FindPath(Vector2 startPos, Vector2 targetPos, float maxDistance = 300.0f)
    {
        Vector2I startGrid = WorldToGrid(startPos);
        Vector2I targetGrid = WorldToGrid(targetPos);

        if (startGrid.DistanceTo(targetGrid) * GRID_SIZE > maxDistance)
        {
            return new List<Vector2>(); // Path too long
        }

        var openSet = new List<PathNode>();
        var closedSet = new HashSet<Vector2I>();

        var startNode = GetOrCreateNode(startGrid);
        var targetNode = GetOrCreateNode(targetGrid);

        startNode.GCost = 0;
        startNode.HCost = CalculateDistance(startGrid, targetGrid);
        openSet.Add(startNode);

        while (openSet.Count > 0)
        {
            var currentNode = openSet.OrderBy(n => n.FCost).First();
            openSet.Remove(currentNode);
            closedSet.Add(WorldToGrid(currentNode.Position));

            if (currentNode.Position == targetNode.Position)
            {
                return ReconstructPath(currentNode);
            }

            foreach (var neighbor in GetNeighbors(currentNode))
            {
                Vector2I neighborGrid = WorldToGrid(neighbor.Position);
                if (closedSet.Contains(neighborGrid) || !neighbor.IsWalkable)
                    continue;

                float tentativeGCost = currentNode.GCost + CalculateDistance(
                    WorldToGrid(currentNode.Position), neighborGrid);

                if (!openSet.Contains(neighbor))
                {
                    openSet.Add(neighbor);
                }
                else if (tentativeGCost >= neighbor.GCost)
                {
                    continue;
                }

                neighbor.Parent = currentNode;
                neighbor.GCost = tentativeGCost;
                neighbor.HCost = CalculateDistance(neighborGrid, targetGrid);
            }
        }

        return new List<Vector2>(); // No path found
    }

    private List<PathNode> GetNeighbors(PathNode node)
    {
        var neighbors = new List<PathNode>();
        Vector2I gridPos = WorldToGrid(node.Position);

        // 8-directional movement
        for (int x = -1; x <= 1; x++)
        {
            for (int y = -1; y <= 1; y++)
            {
                if (x == 0 && y == 0) continue;

                Vector2I neighborGrid = gridPos + new Vector2I(x, y);
                var neighborNode = GetOrCreateNode(neighborGrid);

                // Check if position is walkable
                neighborNode.IsWalkable = IsPositionWalkable(GridToWorld(neighborGrid));
                neighbors.Add(neighborNode);
            }
        }

        return neighbors;
    }

    private bool IsPositionWalkable(Vector2 worldPos)
    {
        Vector2I tilePos = new Vector2I(
            Mathf.FloorToInt(worldPos.X / GRID_SIZE),
            Mathf.FloorToInt(worldPos.Y / GRID_SIZE)
        );

        var tileData = _customDataManager?.GetTileData(tilePos);
        if (tileData == null) return false;

        // Check if enemies can move on this tile
        return tileData.CanEnemy && tileData.ObjectTypePlaced == ObjectTypePlaced.None;
    }

    private List<Vector2> ReconstructPath(PathNode endNode)
    {
        var path = new List<Vector2>();
        var currentNode = endNode;

        while (currentNode != null)
        {
            path.Add(currentNode.Position);
            currentNode = currentNode.Parent;
        }

        path.Reverse();
        return OptimizePath(path);
    }

    private List<Vector2> OptimizePath(List<Vector2> path)
    {
        if (path.Count <= 2) return path;

        var optimizedPath = new List<Vector2> { path[0] };

        for (int i = 1; i < path.Count - 1; i++)
        {
            Vector2 current = path[i];
            Vector2 next = path[i + 1];
            Vector2 previous = optimizedPath.Last();

            // Check if we can skip current waypoint
            if (!CanMoveDirect(previous, next))
            {
                optimizedPath.Add(current);
            }
        }

        optimizedPath.Add(path.Last());
        return optimizedPath;
    }

    private bool CanMoveDirect(Vector2 from, Vector2 to)
    {
        // Ray casting to check if direct movement is possible
        var spaceState = GetWorld2D().DirectSpaceState;
        var query = PhysicsRayQueryParameters2D.Create(from, to);
        query.CollisionMask = 1; // Buildings and obstacles

        var result = spaceState.IntersectRay(query);
        return result.Count == 0;
    }
}
```

### Enhanced Region Integration

#### Region-Specific Enemy Configuration
```csharp
[System.Serializable]
public class RegionEnemyConfig
{
    public int RegionId;
    public bool IsAggressiveRegion;
    public int MaxEnemies;
    public float SpawnInterval;
    public List<EnemySpawnData> EnemyTypes;
    public float DifficultyMultiplier = 1.0f;
}

[System.Serializable]
public class EnemySpawnData
{
    public EnemyType Type;
    public PackedScene Scene;
    public float SpawnWeight; // Probability weight
    public int MinPlayerLevel; // Level requirement
    public List<ResourceType> RequiredResources; // Drops
}

public partial class RegionEnemyManager : Node2D
{
    [Export] public RegionEnemyConfig Config { get; set; }

    private List<BaseEnemy> _activeEnemies = new();
    private Timer _spawnTimer;
    private Timer _aggressionTimer;

    public override void _Ready()
    {
        SetupSpawnTimer();
        SetupAggressionTimer();
        LoadExistingEnemies();

        if (CommonSignals.Instance != null)
        {
            CommonSignals.Instance.EnemyDefeated += OnEnemyDefeated;
        }
    }

    private void SetupAggressionTimer()
    {
        if (!Config.IsAggressiveRegion) return;

        _aggressionTimer = new Timer();
        _aggressionTimer.WaitTime = 5.0f; // Check every 5 seconds
        _aggressionTimer.Timeout += CheckAggressionTrigger;
        _aggressionTimer.Autostart = true;
        AddChild(_aggressionTimer);
    }

    private void CheckAggressionTrigger()
    {
        if (_activeEnemies.Count < 4) return;

        // Find player
        var player = GetNode<PlayerController>("/root/world/Player");
        if (player == null) return;

        // Check if player is in region
        var tileData = GetCustomDataManager()?.GetTileData(
            new Vector2I(
                Mathf.FloorToInt(player.GlobalPosition.X / 16),
                Mathf.FloorToInt(player.GlobalPosition.Y / 16)
            )
        );

        if (tileData?.Region != Config.RegionId) return;

        // Activate aggressive behavior
        var passiveEnemies = _activeEnemies
            .Where(e => e.GetCurrentState() == EnemyState.Patrolling)
            .Take(4)
            .ToList();

        foreach (var enemy in passiveEnemies)
        {
            enemy.SetTarget(player);
            enemy.SetAggressive(true);
        }
    }

    private EnemyType SelectEnemyTypeToSpawn()
    {
        var playerLevel = GameSaveData.Instance.PlayerStats.Level;
        var availableTypes = Config.EnemyTypes
            .Where(e => e.MinPlayerLevel <= playerLevel)
            .ToList();

        if (!availableTypes.Any())
            return EnemyType.Goblin; // Default fallback

        // Weighted random selection
        float totalWeight = availableTypes.Sum(e => e.SpawnWeight);
        float randomValue = (float)GD.Randf() * totalWeight;
        float currentWeight = 0;

        foreach (var enemyData in availableTypes)
        {
            currentWeight += enemyData.SpawnWeight;
            if (randomValue <= currentWeight)
                return enemyData.Type;
        }

        return availableTypes.First().Type;
    }
}
```

This comprehensive design provides the foundation for a sophisticated enemy system that rivals AAA game quality while integrating seamlessly with your existing codebase architecture.
