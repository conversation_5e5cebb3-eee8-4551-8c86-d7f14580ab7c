# Enemy System Architecture & Design

## System Overview

The enemy system is a comprehensive territorial AI system integrated with the existing game architecture. It provides region-based enemy spawning, territorial behavior, combat mechanics, and full save/load persistence.

## Core Architecture

### Base Classes & Interfaces
- **BaseEnemy.cs**: Abstract base class with complete AI state machine, health system, and combat integration
- **MeleeGoblin.cs**: Concrete implementation for territorial melee enemies with charging and attack mechanics
- **ICombatTarget.cs**: Interface for objects that can be targeted by enemy AI
- **EnemyType.cs**: Enum with extension methods providing enemy stats and configuration
- **EnemySaveData.cs**: Data structure for enemy persistence across save/load cycles

### Territory System
- **EnemyTerritory.cs**: Area2D-based territory management with player detection and enemy coordination
- **EnemyTerritory.tscn**: Scene template for territory areas with collision shapes and debug visualization
- Territory areas are placed in world scene under RegionSpecific nodes for organization

### Integration Points
- **Region5Manager.cs**: Complete region manager handling enemy spawning, lifecycle, and save/load
- **CommonSignals.cs**: Extended with enemy-specific signals for combat and state management
- **ResourcesManager.cs**: Extended with enemy data serialization and loading capabilities
- **PlayerController.cs**: Enhanced with TakeDamage method for enemy combat integration

## AI State Machine

### Enemy States
- **Patrolling**: Random movement within territory, idle periods, target detection
- **Pursuing**: Chase player when detected in territory or after being hit
- **Attacking**: Execute melee attacks with windup animations and area damage
- **Returning**: Return to territory center when player leaves or gets too far
- **Stunned**: Brief disable after taking damage with proper animation handling

### Territorial Behavior
- **Territory Centers**: Fixed positions assigned to each enemy for patrolling
- **Territory Radius**: Configurable area (default 120 units) for enemy movement bounds
- **Player Detection**: Enemies become aggressive when player enters territory or hits them
- **Coordination**: Territory areas can alert multiple enemies simultaneously

### Combat Mechanics
- **Melee Attacks**: 180-degree arc damage similar to player sword mechanics
- **Attack Windup**: Timed attack preparation with directional animations
- **Knockback System**: Both player and enemy knockback effects on damage
- **Health System**: HP bars with visibility management and damage feedback
## Animation System

### Required Animations
Each enemy requires 16 directional animations in AnimationPlayer:
- **Idle Animations**: idle_up, idle_down, idle_left, idle_right
- **Movement Animations**: move_up, move_down, move_left, move_right
- **Attack Animations**: attack_up, attack_down, attack_left, attack_right
- **Damage Animations**: got_hit_up, got_hit_down, got_hit_left, got_hit_right

### Animation Behavior
- **Automatic Direction**: System automatically switches animations based on movement/facing direction
- **Attack Direction**: Enemies face targets and use appropriate attack animations
- **Damage Preservation**: Hit animations preserve facing direction after completion
- **State Transitions**: Smooth animation transitions between AI states

## Enemy Types & Characteristics

### Implemented Types
- **Goblin (Melee)**: Territorial close-combat enemy with charging behavior and area attacks
- **Future Types**: Archer (ranged), Necromancer (summoner), Shaman (support), etc.

### Extensible Design
- **EnemyType Enum**: Centralized type definitions with stat extensions
- **Stat System**: Health, damage, speed, detection range, attack range, XP rewards
- **Behavioral Variants**: Different AI patterns per enemy type while sharing base systems
## Spawning & Region Management

### Region Integration
- **Region5Manager.cs**: Complete implementation handling enemy lifecycle in Region 5
- **Spawn Limits**: 2 enemies maximum per region with distance-based collision avoidance
- **Territory Assignment**: Enemies assigned to EnemyTerritory areas for coordinated behavior
- **Spawn Validation**: Checks for valid tiles, existing enemy proximity, and static object collision

### Save/Load System
- **EnemySaveData.cs**: Complete persistence structure for enemy state, position, health, territory
- **ResourcesManager.cs**: Extended with enemy data serialization using GlobalJsonOptions
- **Region Persistence**: Enemies saved per region with full state restoration on load

## Combat Integration

### Player Combat Interface
- **ICombatTarget Interface**: Allows enemies to target player and buildings uniformly
- **Damage System**: Integrated with existing IDestroyableObject interface
- **Attack Mechanics**: 180-degree arc attacks similar to player sword system
- **Knockback Effects**: Both player and enemy knockback on damage

### Signal System
- **CommonSignals.cs**: Extended with enemy-specific events for combat coordination
- **Event Integration**: Enemy attacks, defeats, and spawns broadcast to game systems
- **Combat Feedback**: Damage numbers, sound effects, and visual feedback integration
## Performance & Optimization

### Update Management
- **Distance-Based Updates**: Only enemies near player receive full AI updates
- **Spatial Partitioning**: Grid-based system for efficient enemy queries and collision detection
- **State Optimization**: Distant enemies use simplified state updates

### Memory Management
- **Object Pooling**: Reuse enemy instances and projectiles to reduce garbage collection
- **Efficient Pathfinding**: Cached paths and simplified navigation for distant enemies
- **Resource Cleanup**: Proper disposal of enemy resources on death or region unload

## Technical Implementation Notes

### Integration Requirements
- **Collision Layers**: Enemies use dedicated collision layers separate from player and buildings
- **Signal Architecture**: All enemy events broadcast through CommonSignals for loose coupling
- **Save Compatibility**: Enemy data integrates with existing GameSaveData structure
- **Region Coordination**: Enemies managed by region-specific managers following established patterns

### Error Handling & Robustness
- **Null Safety**: All target and node references validated before use
- **Pathfinding Fallbacks**: Simple direct movement when complex pathfinding fails
- **State Recovery**: Enemies can recover from invalid states and positions
- **Save Data Validation**: Corrupted enemy data handled gracefully with defaults
## System Design Philosophy

### Territorial AI Concept
The enemy system is built around territorial behavior where enemies have defined areas they patrol and defend. This creates predictable yet dynamic encounters where players can learn enemy patterns while still facing varied combat scenarios.

### Modular Architecture
The system uses a modular design where base functionality is shared through BaseEnemy.cs while specific behaviors are implemented in derived classes like MeleeGoblin.cs. This allows for easy extension to new enemy types while maintaining consistent core mechanics.

### Integration-First Design
Rather than being a standalone system, the enemy architecture is designed to integrate seamlessly with existing game systems including region management, save/load, combat mechanics, and UI systems. This ensures consistency with the overall game architecture.
