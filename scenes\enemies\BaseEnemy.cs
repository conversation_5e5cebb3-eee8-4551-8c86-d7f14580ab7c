using Godot;
using System;
using System.Collections.Generic;
using System.Linq;

/// <summary>
/// Base class for all enemies in the game
/// Provides core functionality for AI, combat, movement, and territory management
/// </summary>
public abstract partial class BaseEnemy : CharacterBody2D, IDestroyableObject, IEnemy
{
	// Core Properties
	[Export] public EnemyType EnemyType { get; set; } = EnemyType.Goblin;
	[Export] public int MaxHealth { get; set; } = 20;
	[Export] public int AttackDamage { get; set; } = 5;
	[Export] public float MovementSpeed { get; set; } = 30.0f;
	[Export] public float DetectionRange { get; set; } = 80.0f;
	[Export] public float AttackRange { get; set; } = 24.0f;
	[Export] public float TerritoryRadius { get; set; } = 120.0f;
	[Export] public int XpReward { get; set; } = 15;
	[Export] public float AttackCooldown { get; set; } = 2.0f;
	[Export] public float PatrolInterval { get; set; } = 3.0f;
	[Export] public float StunDuration { get; set; } = 0.5f;

	// Territory & AI
	protected Vector2 _territoryCenter;
	protected EnemyState _currentState = EnemyState.Patrolling;
	protected Node2D _currentTarget;
	protected float _lastAttackTime = 0.0f;
	protected bool _isAggressive = false;
	protected int _assignedRegion = 1;
	protected bool _wasHitByPlayer = false;
	protected Vector2 _lastKnownPlayerPosition;

	// Movement & Pathfinding
	protected Vector2 _startPosition;
	protected Vector2 _targetPosition;
	protected float _movementProgress = 0.0f;
	protected bool _isMoving = false;

	// Components
	protected int _currentHealth;
	protected Sprite2D _sprite;
	protected AnimationPlayer _animationPlayer;
	protected ProgressBar _hpBar;
	protected Timer _stateTimer;
	protected Timer _attackTimer;
	protected CustomDataLayerManager _customDataManager;
	protected Area2D _detectionArea;
	protected Area2D _attackArea;
	protected CollisionShape2D _detectionShape;
	protected CollisionShape2D _attackShape;

	// State management
	protected bool _isBeingDestroyed = false;
	protected Vector2I _tilePosition;
	protected Random _random = new();
	protected string _lastDirection = "down";
	protected bool _isIdling = false;
	protected float _idleStartTime = 0.0f;
	protected float _idleDuration = 2.0f;

	public override void _Ready()
	{
		// Initialize health from enemy type
		_currentHealth = EnemyType.GetMaxHealth();
		MaxHealth = _currentHealth;
		AttackDamage = EnemyType.GetAttackDamage();
		MovementSpeed = EnemyType.GetMovementSpeed();
		DetectionRange = EnemyType.GetDetectionRange();
		AttackRange = EnemyType.GetAttackRange();
		XpReward = EnemyType.GetXpReward();

		SetupComponents();
		SetupDetectionArea();
		SetupAttackArea();
		SetupTimers();

		_customDataManager = GetNode<CustomDataLayerManager>("/root/world/CustomDataLayerManager");

		// Connect to combat signals
		if (CommonSignals.Instance != null)
		{
			CommonSignals.Instance.SwordUsed += OnSwordUsed;
		}

		// Set initial territory center to spawn position
		if (_territoryCenter == Vector2.Zero)
		{
			_territoryCenter = GlobalPosition;
		}

		UpdateTilePosition();
		UpdateHPBar();
		ChangeState(EnemyState.Patrolling);

		// Emit spawn signal
		CommonSignals.Instance?.EmitEnemySpawned(EnemyType, GlobalPosition, _assignedRegion);
	}

	protected virtual void SetupComponents()
	{
		_sprite = GetNode<Sprite2D>("Sprite2D");
		_animationPlayer = GetNode<AnimationPlayer>("AnimationPlayer");
		_hpBar = GetNode<ProgressBar>("ProgressBar");

		if (_hpBar != null)
		{
			_hpBar.Visible = false; // Hide initially
		}
	}

	protected virtual void SetupDetectionArea()
	{
		_detectionArea = new Area2D();
		_detectionArea.Name = "DetectionArea";
		_detectionShape = new CollisionShape2D();
		var detectionCircle = new CircleShape2D();
		detectionCircle.Radius = DetectionRange;
		_detectionShape.Shape = detectionCircle;
		_detectionArea.AddChild(_detectionShape);
		AddChild(_detectionArea);

		_detectionArea.BodyEntered += OnDetectionAreaEntered;
		_detectionArea.BodyExited += OnDetectionAreaExited;
		_detectionArea.AreaEntered += OnDetectionAreaEntered;
	}

	protected virtual void SetupAttackArea()
	{
		_attackArea = new Area2D();
		_attackArea.Name = "AttackArea";
		_attackShape = new CollisionShape2D();
		var attackCircle = new CircleShape2D();
		attackCircle.Radius = AttackRange;
		_attackShape.Shape = attackCircle;
		_attackArea.AddChild(_attackShape);
		AddChild(_attackArea);
	}

	protected virtual void SetupTimers()
	{
		_stateTimer = new Timer();
		_stateTimer.OneShot = true;
		_stateTimer.Timeout += OnStateTimerTimeout;
		AddChild(_stateTimer);

		_attackTimer = new Timer();
		_attackTimer.WaitTime = AttackCooldown;
		_attackTimer.OneShot = true;
		AddChild(_attackTimer);
	}

	public override void _PhysicsProcess(double delta)
	{
		if (_isBeingDestroyed) return;

		UpdateState(delta);
		HandleMovement(delta);
		CheckTargetDistance();
		UpdateTilePosition();
	}

	protected virtual void UpdateState(double delta)
	{
		switch (_currentState)
		{
			case EnemyState.Patrolling:
				HandlePatrolling(delta);
				break;
			case EnemyState.Pursuing:
				HandlePursuit(delta);
				break;
			case EnemyState.Attacking:
				HandleAttacking(delta);
				break;
			case EnemyState.Returning:
				HandleReturning(delta);
				break;
			case EnemyState.Stunned:
				HandleStunned(delta);
				break;
		}
	}

	protected virtual void HandlePatrolling(double delta)
	{
		if (!_isMoving && !_isIdling)
		{
			// Start idling
			_isIdling = true;
			_idleStartTime = Time.GetTicksMsec() / 1000.0f;
			_idleDuration = (float)(_random.NextDouble() * 3.0f + 1.0f); // 1-4 seconds idle
			PlayAnimation("idle_" + _lastDirection);
		}
		else if (_isIdling)
		{
			// Check if idle period is over
			float currentTime = Time.GetTicksMsec() / 1000.0f;
			if (currentTime - _idleStartTime >= _idleDuration)
			{
				_isIdling = false;
				ChoosePatrolTarget();
			}
		}

		// Always check for targets during patrolling (territorial behavior)
		CheckForTargets();
	}

	protected virtual void HandlePursuit(double delta)
	{
		if (_currentTarget == null || !IsInstanceValid(_currentTarget))
		{
			ChangeState(EnemyState.Patrolling);
			return;
		}

		// Check if target is in attack range
		float distanceToTarget = GlobalPosition.DistanceTo(_currentTarget.GlobalPosition);
		if (distanceToTarget <= AttackRange)
		{
			ChangeState(EnemyState.Attacking);
			return;
		}

		// Check if we should return to territory
		if (ShouldReturnToTerritory())
		{
			ChangeState(EnemyState.Returning);
			return;
		}

		// Move towards target
		MoveTowardsTarget(_currentTarget.GlobalPosition);
	}

	protected virtual void HandleAttacking(double delta)
	{
		if (_currentTarget == null || !IsInstanceValid(_currentTarget))
		{
			ChangeState(EnemyState.Patrolling);
			return;
		}

		// Check if target is still in range
		float distanceToTarget = GlobalPosition.DistanceTo(_currentTarget.GlobalPosition);
		if (distanceToTarget > AttackRange)
		{
			ChangeState(EnemyState.Pursuing);
			return;
		}

		// Attack if cooldown is ready
		if (_attackTimer.IsStopped())
		{
			ExecuteAttack();
			_attackTimer.Start();
		}
	}

	protected virtual void HandleReturning(double delta)
	{
		// Move back to territory center
		MoveTowardsTarget(_territoryCenter);

		// Check if we're back in territory
		if (GlobalPosition.DistanceTo(_territoryCenter) <= TerritoryRadius * 0.8f)
		{
			ChangeState(EnemyState.Patrolling);
		}
	}

	protected virtual void HandleStunned(double delta)
	{
		// Do nothing while stunned - timer will handle state change
	}

	protected virtual void HandleMovement(double delta)
	{
		if (!_isMoving || _currentState == EnemyState.Stunned) return;

		if (_startPosition.DistanceTo(_targetPosition) > 0.1f)
		{
			_movementProgress += (float)(MovementSpeed * delta / _startPosition.DistanceTo(_targetPosition));
			_movementProgress = Mathf.Clamp(_movementProgress, 0.0f, 1.0f);

			GlobalPosition = _startPosition.Lerp(_targetPosition, _movementProgress);

			if (_movementProgress >= 1.0f)
			{
				_isMoving = false;
				_movementProgress = 0.0f;
			}
		}
		else
		{
			_isMoving = false;
		}
	}

	protected virtual void ChoosePatrolTarget()
	{
		// Choose a random point within territory
		float angle = (float)(_random.NextDouble() * Math.PI * 2);
		float distance = (float)(_random.NextDouble() * TerritoryRadius * 0.8f);
		
		Vector2 patrolTarget = _territoryCenter + new Vector2(
			Mathf.Cos(angle) * distance,
			Mathf.Sin(angle) * distance
		);

		MoveTowardsTarget(patrolTarget);
	}

	protected virtual void MoveTowardsTarget(Vector2 targetPos)
	{
		_startPosition = GlobalPosition;
		_targetPosition = targetPos;
		_movementProgress = 0.0f;
		_isMoving = true;

		// Update facing direction
		Vector2 direction = (targetPos - GlobalPosition).Normalized();
		UpdateFacingDirection(direction);
	}

	protected virtual void UpdateFacingDirection(Vector2 direction)
	{
		// Update last direction for consistent facing
		_lastDirection = GetDirectionString(direction);

		// Play appropriate movement animation based on direction
		string animationName = "move_" + _lastDirection;
		PlayAnimation(animationName);
	}

	protected virtual string GetDirectionString(Vector2 direction)
	{
		if (Math.Abs(direction.X) > Math.Abs(direction.Y))
		{
			return direction.X > 0 ? "right" : "left";
		}
		else
		{
			return direction.Y > 0 ? "down" : "up";
		}
	}

	protected virtual void PlayAnimation(string animationName)
	{
		if (_animationPlayer != null && _animationPlayer.HasAnimation(animationName))
		{
			_animationPlayer.Play(animationName);
		}
	}

	protected virtual void CheckForTargets()
	{
		// Find the best target within detection range
		var potentialTargets = new List<Node2D>();

		// Check for player
		var player = GetNode<PlayerController>("/root/world/Player");
		if (player != null && GlobalPosition.DistanceTo(player.GlobalPosition) <= DetectionRange)
		{
			potentialTargets.Add(player);
		}

		// TODO: Add building detection when building system is extended with ICombatTarget

		if (potentialTargets.Count > 0)
		{
			_currentTarget = SelectBestTarget(potentialTargets);
			if (_currentTarget != null)
			{
				ChangeState(EnemyState.Pursuing);
			}
		}
	}

	protected virtual Node2D SelectBestTarget(List<Node2D> targets)
	{
		// For now, prioritize player
		var player = targets.FirstOrDefault(t => t is PlayerController);
		return player ?? targets.FirstOrDefault();
	}

	protected virtual bool ShouldReturnToTerritory()
	{
		float distanceFromTerritory = GlobalPosition.DistanceTo(_territoryCenter);
		return distanceFromTerritory > TerritoryRadius;
	}

	protected virtual void CheckTargetDistance()
	{
		if (_currentTarget == null || !IsInstanceValid(_currentTarget)) return;

		float distance = GlobalPosition.DistanceTo(_currentTarget.GlobalPosition);
		
		// Lose target if too far away
		if (distance > DetectionRange * 1.5f)
		{
			_currentTarget = null;
			if (_currentState == EnemyState.Pursuing || _currentState == EnemyState.Attacking)
			{
				ChangeState(EnemyState.Returning);
			}
		}
	}

	protected abstract void ExecuteAttack();

	protected virtual void ChangeState(EnemyState newState)
	{
		if (_currentState == newState) return;

		_currentState = newState;
		OnStateChanged(newState);
	}

	protected virtual void OnStateChanged(EnemyState newState)
	{
		switch (newState)
		{
			case EnemyState.Patrolling:
				PlayAnimation("idle_" + _lastDirection);
				_stateTimer.WaitTime = PatrolInterval;
				_stateTimer.Start();
				break;
			case EnemyState.Pursuing:
				// Animation handled in movement
				break;
			case EnemyState.Attacking:
				// Animation handled in attack
				break;
			case EnemyState.Returning:
				// Animation handled in movement
				break;
			case EnemyState.Stunned:
				PlayAnimation("got_hit_" + _lastDirection);
				_stateTimer.WaitTime = StunDuration;
				_stateTimer.Start();
				break;
		}
	}

	protected virtual void OnStateTimerTimeout()
	{
		switch (_currentState)
		{
			case EnemyState.Patrolling:
				if (!_isMoving)
				{
					ChoosePatrolTarget();
				}
				_stateTimer.WaitTime = PatrolInterval;
				_stateTimer.Start();
				break;
			case EnemyState.Stunned:
				ChangeState(EnemyState.Patrolling);
				break;
		}
	}

	// IDestroyableObject implementation
	public virtual void TakeDamage(int damage)
	{
		if (_isBeingDestroyed) return;

		_currentHealth -= damage;
		UpdateHPBar();
		ShowHPBar();

		_wasHitByPlayer = true;
		ChangeState(EnemyState.Stunned);

		CommonSignals.Instance?.EmitEnemyTookDamage(EnemyType, GlobalPosition, damage);

		if (_currentHealth <= 0)
		{
			Die();
		}
	}

	public virtual bool CanBeHitFrom(Vector2I playerTilePosition)
	{
		Vector2I enemyTile = new Vector2I(
			Mathf.FloorToInt(GlobalPosition.X / 16),
			Mathf.FloorToInt(GlobalPosition.Y / 16)
		);
		
		float distance = playerTilePosition.DistanceTo(enemyTile);
		return distance <= 2.0f;
	}

	public virtual Vector2I GetTilePosition()
	{
		return _tilePosition;
	}

	public virtual int GetCurrentHealth()
	{
		return _currentHealth;
	}

	public virtual void SetCurrentHealth(int health)
	{
		_currentHealth = health;
		UpdateHPBar();
	}

	// IEnemy implementation
	public EnemyState GetCurrentState()
	{
		return _currentState;
	}

	public void SetTarget(Node2D target)
	{
		_currentTarget = target;
		if (target != null)
		{
			ChangeState(EnemyState.Pursuing);
		}
	}

	public void SetTerritory(Vector2 center, float radius)
	{
		_territoryCenter = center;
		TerritoryRadius = radius;
	}

	public void SetAggressive(bool aggressive)
	{
		_isAggressive = aggressive;
	}

	public EnemyType GetEnemyType()
	{
		return EnemyType;
	}

	public int GetRegion()
	{
		return _assignedRegion;
	}

	public void SetRegion(int regionId)
	{
		_assignedRegion = regionId;
	}

	protected virtual void UpdateTilePosition()
	{
		_tilePosition = new Vector2I(
			Mathf.FloorToInt(GlobalPosition.X / 16),
			Mathf.FloorToInt(GlobalPosition.Y / 16)
		);
	}

	protected virtual void UpdateHPBar()
	{
		if (_hpBar != null)
		{
			float healthPercentage = (float)_currentHealth / MaxHealth;
			_hpBar.SetProgress(healthPercentage);
			GD.Print($"Enemy HP Bar: {_currentHealth}/{MaxHealth} = {healthPercentage:F2} ({healthPercentage * 100:F1}%)");
		}
		else
		{
			GD.PrintErr("Enemy HP Bar is null!");
		}
	}

	protected virtual void ShowHPBar()
	{
		if (_hpBar != null)
		{
			_hpBar.Visible = true;
			// Hide HP bar after 3 seconds
			GetTree().CreateTimer(3.0f).Timeout += () => {
				if (_hpBar != null && IsInstanceValid(this))
				{
					_hpBar.Visible = false;
				}
			};
		}
	}

	protected virtual void Die()
	{
		if (_isBeingDestroyed) return;
		_isBeingDestroyed = true;

		DropResources();
		CommonSignals.Instance?.EmitAddXp(XpReward);
		CommonSignals.Instance?.EmitEnemyDefeated(EnemyType, GlobalPosition, XpReward);

		// Clear tile occupation if set
		if (_customDataManager != null)
		{
			_customDataManager.ClearObjectPlaced(_tilePosition);
		}

		QueueFree();
	}

	protected virtual void DropResources()
	{
		// Base enemies don't drop resources by default
		// Override in specific enemy types to add drops
	}

	protected virtual void OnDetectionAreaEntered(Node2D body)
	{
		if (body is PlayerController && (_isAggressive || _wasHitByPlayer))
		{
			if (_currentTarget == null)
			{
				SetTarget(body);
			}
		}
	}

	protected virtual void OnDetectionAreaExited(Node2D body)
	{
		// Target will be lost through distance checking
	}

	protected virtual void OnSwordUsed(Vector2I tilePosition, Vector2 playerPosition, Vector2 attackDirection)
	{
		// Check if this enemy is in the sword's attack arc
		if (IsInSwordAttackArc(playerPosition, attackDirection))
		{
			ApplyKnockback(playerPosition);
			TakeDamage(2); // Sword damage
		}
	}

	protected virtual bool IsInSwordAttackArc(Vector2 playerPosition, Vector2 attackDirection)
	{
		float distance = GlobalPosition.DistanceTo(playerPosition);
		if (distance > 32.0f) return false; // Sword range

		Vector2 directionToEnemy = (GlobalPosition - playerPosition).Normalized();
		float dotProduct = attackDirection.Dot(directionToEnemy);
		
		return dotProduct > 0.5f; // ~60 degree arc
	}

	protected virtual void ApplyKnockback(Vector2 playerPosition)
	{
		const float KnockbackForce = 24.0f;
		const float KnockbackDuration = 0.2f;

		Vector2 knockbackDirection = (GlobalPosition - playerPosition).Normalized();
		Vector2 targetPosition = GlobalPosition + knockbackDirection * KnockbackForce;

		var knockbackTween = CreateTween();
		knockbackTween.TweenProperty(this, "global_position", targetPosition, KnockbackDuration);
		knockbackTween.SetEase(Tween.EaseType.Out);
		knockbackTween.SetTrans(Tween.TransitionType.Quart);
	}

	public virtual EnemySaveData GetSaveData()
	{
		return new EnemySaveData
		{
			Position = GlobalPosition,
			Health = _currentHealth,
			AssignedRegion = _assignedRegion,
			EnemyType = EnemyType,
			CurrentState = _currentState,
			TerritoryCenter = _territoryCenter,
			TerritoryRadius = TerritoryRadius,
			IsAggressive = _isAggressive,
			LastAttackTime = _lastAttackTime,
			WasHitByPlayer = _wasHitByPlayer,
			LastKnownPlayerPosition = _lastKnownPlayerPosition,
			StartPosition = _startPosition,
			TargetPosition = _targetPosition,
			MovementProgress = _movementProgress
		};
	}

	public virtual void LoadFromSaveData(EnemySaveData data)
	{
		GlobalPosition = data.Position;
		_currentHealth = data.Health;
		_assignedRegion = data.AssignedRegion;
		EnemyType = data.EnemyType;
		_territoryCenter = data.TerritoryCenter;
		TerritoryRadius = data.TerritoryRadius;
		_isAggressive = data.IsAggressive;
		_lastAttackTime = data.LastAttackTime;
		_wasHitByPlayer = data.WasHitByPlayer;
		_lastKnownPlayerPosition = data.LastKnownPlayerPosition;
		_startPosition = data.StartPosition;
		_targetPosition = data.TargetPosition;
		_movementProgress = data.MovementProgress;

		UpdateTilePosition();
		CallDeferred(nameof(InitializeStateAfterReady), (int)data.CurrentState);
	}

	protected virtual void InitializeStateAfterReady(int stateInt)
	{
		if (_animationPlayer != null)
		{
			EnemyState state = (EnemyState)stateInt;
			ChangeState(state);
		}
	}

	public override void _ExitTree()
	{
		if (CommonSignals.Instance != null)
		{
			CommonSignals.Instance.SwordUsed -= OnSwordUsed;
		}
		base._ExitTree();
	}
}
