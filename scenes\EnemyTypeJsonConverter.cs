using System;
using System.Text.Json;
using System.Text.Json.Serialization;

/// <summary>
/// Custom JSON converter for EnemyType enum that handles string values
/// </summary>
public class EnemyTypeJsonConverter : JsonConverter<EnemyType>
{
    public override EnemyType Read(ref Utf8JsonReader reader, Type typeToConvert, JsonSerializerOptions options)
    {
        if (reader.TokenType == JsonTokenType.String)
        {
            var stringValue = reader.GetString();
            
            // Handle the string values from JSON
            return stringValue?.ToLowerInvariant() switch
            {
                "goblin" => EnemyType.Goblin,
                "orc" => EnemyType.Orc,
                "skeleton" => EnemyType.Skeleton,
                "archer" => EnemyType.Archer,
                "mage" => EnemyType.Mage,
                "necromancer" => EnemyType.Necromancer,
                "shaman" => EnemyType.Shaman,
                "goblinking" => EnemyType.GoblinKing,
                "dragonlord" => EnemyType.DragonLord,
                _ => EnemyType.Goblin // Default fallback
            };
        }
        
        if (reader.TokenType == JsonTokenType.Number)
        {
            // Handle numeric values (enum index)
            var intValue = reader.GetInt32();
            return (EnemyType)intValue;
        }
        
        throw new JsonException($"Unable to parse EnemyType from {reader.TokenType}");
    }

    public override void Write(Utf8JsonWriter writer, EnemyType value, JsonSerializerOptions options)
    {
        // Write as string for better readability in JSON
        writer.WriteStringValue(value.ToString());
    }
}
