using Godot;
using System;
using System.Collections.Generic;

/// <summary>
/// Melee Goblin enemy implementation
/// Territorial enemy that attacks with melee weapons when player enters territory
/// </summary>
public partial class MeleeGoblin : BaseEnemy
{
	[Export] public float ChargeSpeedMultiplier { get; set; } = 1.5f;
	[Export] public float AttackWindupTime { get; set; } = 0.8f;
	[Export] public ResourceType DropResourceType { get; set; } = ResourceType.Wood;
	[Export] public int DropAmount { get; set; } = 1;
	[Export] public int GoldDropAmount { get; set; } = 5;
	[Export] public int XpDropAmount { get; set; } = 15;
	[Export] public float AttackArcAngle { get; set; } = 180.0f; // Attack arc in degrees
	[Export] public float AttackDistance { get; set; } = 32.0f; // Attack reach distance

	private float _attackStartTime = 0.0f;
	private bool _isCharging = false;
	private bool _isWindingUp = false;
	private Vector2 _attackTargetPosition = Vector2.Zero;
	private float _baseMovementSpeed;

	public override void _Ready()
	{
		// Set goblin-specific properties
		EnemyType = EnemyType.Goblin;
		MaxHealth = EnemyType.Goblin.GetMaxHealth();
		AttackDamage = EnemyType.Goblin.GetAttackDamage();
		MovementSpeed = EnemyType.Goblin.GetMovementSpeed();
		DetectionRange = EnemyType.Goblin.GetDetectionRange();
		AttackRange = EnemyType.Goblin.GetAttackRange();
		XpReward = EnemyType.Goblin.GetXpReward();
		
		// Goblins are territorial but not initially aggressive
		_isAggressive = false;

		// Store base movement speed
		_baseMovementSpeed = MovementSpeed;

		base._Ready();
	}

	protected override void HandlePursuit(double delta)
	{
		if (_currentTarget == null || !IsInstanceValid(_currentTarget))
		{
			ChangeState(EnemyState.Patrolling);
			return;
		}

		float distanceToTarget = GlobalPosition.DistanceTo(_currentTarget.GlobalPosition);

		// Start charging when close enough
		if (distanceToTarget <= AttackRange * 2.0f && !_isCharging)
		{
			_isCharging = true;
			// CRITICAL FIX: Use proper direction instead of hardcoded "move_down"
			Vector2 directionToTarget = (_currentTarget.GlobalPosition - GlobalPosition).Normalized();
			_lastDirection = GetDirectionString(directionToTarget);
			PlayAnimation("move_" + _lastDirection);
		}

		// Check if target is in attack range
		if (distanceToTarget <= AttackRange)
		{
			_isCharging = false;
			ChangeState(EnemyState.Attacking);
			return;
		}

		// Check if we should return to territory
		if (ShouldReturnToTerritory())
		{
			_isCharging = false;
			ChangeState(EnemyState.Returning);
			return;
		}

		// Set movement speed based on charging state
		float currentSpeed = _isCharging ? _baseMovementSpeed * ChargeSpeedMultiplier : _baseMovementSpeed;
		MovementSpeed = currentSpeed;

		// Move towards target but stop at optimal combat distance
		if (distanceToTarget > OptimalCombatDistance)
		{
			Vector2 directionToTarget = (_currentTarget.GlobalPosition - GlobalPosition).Normalized();
			Vector2 optimalPosition = _currentTarget.GlobalPosition - directionToTarget * OptimalCombatDistance;
			MoveTowardsTarget(optimalPosition);
		}
		else
		{
			// We're at optimal distance, stop moving and face target
			_isMoving = false;
			Vector2 directionToTarget = (_currentTarget.GlobalPosition - GlobalPosition).Normalized();
			_lastDirection = GetDirectionString(directionToTarget);
			PlayAnimation("idle_" + _lastDirection);
		}
	}

	protected override void HandleAttacking(double delta)
	{
		if (_currentTarget == null || !IsInstanceValid(_currentTarget))
		{
			ChangeState(EnemyState.Patrolling);
			return;
		}

		// Check if target is still in range
		float distanceToTarget = GlobalPosition.DistanceTo(_currentTarget.GlobalPosition);
		if (distanceToTarget > AttackRange)
		{
			ChangeState(EnemyState.Pursuing);
			return;
		}

		// Start attack windup if not already attacking
		if (!_isWindingUp && _attackTimer.IsStopped())
		{
			StartAttackWindup();
		}

		// Execute attack after windup
		if (_isWindingUp)
		{
			float currentTime = Time.GetTicksMsec() / 1000.0f;
			if (currentTime - _attackStartTime >= AttackWindupTime)
			{
				ExecuteAttack();
				_isWindingUp = false;
				_attackTimer.Start();
			}
		}
	}

	protected override void HandleReturning(double delta)
	{
		_isCharging = false;
		// Restore normal movement speed when returning
		MovementSpeed = _baseMovementSpeed;
		base.HandleReturning(delta);
	}

	protected override void OnStateChanged(EnemyState newState)
	{
		switch (newState)
		{
			case EnemyState.Patrolling:
				_isCharging = false;
				_isWindingUp = false;
				// Restore normal movement speed when patrolling
				MovementSpeed = _baseMovementSpeed;
				// Only start patrol timer if not coming from stunned state with idle flag
				if (!_isIdling)
				{
					_stateTimer.WaitTime = PatrolInterval;
					_stateTimer.Start();
				}
				PlayAnimation("idle_" + _lastDirection);
				break;
			case EnemyState.Pursuing:
				_isWindingUp = false;
				// Animation handled in movement
				break;
			case EnemyState.Attacking:
				// Animation handled in attack windup
				break;
			case EnemyState.Returning:
				_isCharging = false;
				_isWindingUp = false;
				// Animation handled in movement
				break;
			case EnemyState.Stunned:
				_isCharging = false;
				_isWindingUp = false;
				// Stop any movement and play hit animation in current direction
				_isMoving = false;
				MovementSpeed = _baseMovementSpeed; // Reset speed
				PlayAnimation("got_hit_" + _lastDirection);
				_stateTimer.WaitTime = StunDuration;
				_stateTimer.Start();
				break;
		}
	}

	private void StartAttackWindup()
	{
		_isWindingUp = true;
		_attackStartTime = Time.GetTicksMsec() / 1000.0f;

		// Store target position when attack starts (like player sword)
		if (_currentTarget != null)
		{
			_attackTargetPosition = _currentTarget.GlobalPosition;
			Vector2 directionToTarget = (_attackTargetPosition - GlobalPosition).Normalized();
			_lastDirection = GetDirectionString(directionToTarget);
		}

		PlayAnimation("attack_" + _lastDirection);
	}

	protected override void ExecuteAttack()
	{
		// Execute attack similar to player sword - 180 degree arc towards stored target position
		Vector2 attackDirection = (_attackTargetPosition - GlobalPosition).Normalized();

		// Find all potential targets in attack range
		var potentialTargets = new List<Node2D>();

		// Check for player
		var player = GetNode<PlayerController>("/root/world/Player");
		if (player != null && IsInAttackArc(player.GlobalPosition, attackDirection))
		{
			potentialTargets.Add(player);
		}

		// TODO: Add building detection when ICombatTarget is implemented

		bool hitSomething = false;

		// Apply damage to all targets in arc
		foreach (var target in potentialTargets)
		{
			if (target is PlayerController playerTarget)
			{
				ApplyAttackToPlayer(playerTarget);
				hitSomething = true;
			}
		}

		// Emit attack signal
		CommonSignals.Instance?.EmitEnemyAttack(GlobalPosition, AttackDamage, EnemyType);

		// Play attack sound/effects
		PlayAttackEffects();

		GD.Print($"Goblin attacked in direction {_lastDirection}! Hit something: {hitSomething}");
	}

	private bool IsInAttackArc(Vector2 targetPosition, Vector2 attackDirection)
	{
		float distance = GlobalPosition.DistanceTo(targetPosition);
		if (distance > AttackDistance) return false;

		Vector2 directionToTarget = (targetPosition - GlobalPosition).Normalized();
		float dotProduct = attackDirection.Dot(directionToTarget);

		// Convert arc angle to dot product threshold
		float arcRadians = AttackArcAngle * Mathf.Pi / 180.0f;
		float threshold = Mathf.Cos(arcRadians / 2.0f);

		return dotProduct >= threshold;
	}

	private void ApplyAttackToPlayer(PlayerController player)
	{
		// Apply damage to player (no knockback)
		if (player.HasMethod("TakeDamage"))
		{
			player.Call("TakeDamage", AttackDamage);
		}
	}

	private void PlayAttackEffects()
	{
		// TODO: Add attack sound effects and visual effects
		// For now, just print debug info
		GD.Print($"Goblin attack effects at {GlobalPosition}");
	}

	protected override void UpdateFacingDirection(Vector2 direction)
	{
		// Update last direction for consistent facing
		_lastDirection = GetDirectionString(direction);

		// Play appropriate movement animation based on direction
		string animationName = "move_" + _lastDirection;
		PlayAnimation(animationName);
	}



	protected override void DropResources()
	{
		// Drop gold ore (as gold currency)
		if (GoldDropAmount > 0)
		{
			DroppedResource.SpawnResource(GlobalPosition, ResourceType.GoldOre, GoldDropAmount);
		}

		// Drop additional resource
		if (DropAmount > 0)
		{
			DroppedResource.SpawnResource(GlobalPosition, DropResourceType, DropAmount);
		}
	}

	protected override void CheckForTargets()
	{
		// Goblins are territorial - they only attack if player enters their territory
		// or if they were hit by the player
		if (!_wasHitByPlayer)
		{
			// Check if player is in territory
			var player = GetNode<PlayerController>("/root/world/Player");
			if (player != null)
			{
				float distanceFromTerritory = player.GlobalPosition.DistanceTo(_territoryCenter);
				if (distanceFromTerritory <= TerritoryRadius)
				{
					float distanceToPlayer = GlobalPosition.DistanceTo(player.GlobalPosition);
					if (distanceToPlayer <= DetectionRange)
					{
						_currentTarget = player;
						ChangeState(EnemyState.Pursuing);
						GD.Print($"Goblin detected player in territory! Distance: {distanceToPlayer}");
					}
				}
			}
		}
		else
		{
			// If hit by player, become aggressive and chase
			base.CheckForTargets();
		}
	}

	public override void TakeDamage(int damage)
	{
		base.TakeDamage(damage);

		// Goblins become aggressive when hit
		_isAggressive = true;
		_wasHitByPlayer = true;

		GD.Print($"Goblin took {damage} damage! Health: {_currentHealth}/{MaxHealth}");
	}

	protected override void Die()
	{
		if (_isBeingDestroyed) return;
		_isBeingDestroyed = true;

		// Play die animation
		PlayAnimation("die");

		// Wait for die animation to complete, then drop resources and XP
		float animationLength = GetDieAnimationLength();
		GetTree().CreateTimer(animationLength).Timeout += () => {
			if (IsInstanceValid(this))
			{
				DropResources();
				CommonSignals.Instance?.EmitAddXp(XpDropAmount);
				CommonSignals.Instance?.EmitEnemyDefeated(EnemyType, GlobalPosition, XpDropAmount);

				// Clear tile occupation if set
				if (_customDataManager != null)
				{
					_customDataManager.ClearObjectPlaced(_tilePosition);
				}

				QueueFree();
			}
		};
	}

	private float GetDieAnimationLength()
	{
		if (_animationPlayer != null && _animationPlayer.HasAnimation("die"))
		{
			var animation = _animationPlayer.GetAnimation("die");
			return animation.Length / _animationPlayer.SpeedScale;
		}
		return 1.0f; // Default 1 second if no animation
	}

	// Territory management for Area2D approach
	public void SetTerritoryArea(Area2D territoryArea)
	{
		if (territoryArea != null)
		{
			// Connect to territory area signals for better territory management
			territoryArea.BodyEntered += OnTerritoryEntered;
			territoryArea.BodyExited += OnTerritoryExited;
		}
	}

	private void OnTerritoryEntered(Node2D body)
	{
		if (body is PlayerController player && !_wasHitByPlayer)
		{
			// Player entered territory - become alert
			if (_currentTarget == null)
			{
				_currentTarget = player;
				ChangeState(EnemyState.Pursuing);
				GD.Print("Goblin: Player entered territory!");
			}
		}
	}

	private void OnTerritoryExited(Node2D body)
	{
		if (body is PlayerController && !_wasHitByPlayer)
		{
			// Player left territory - return to patrolling if not aggressive
			if (!_isAggressive && _currentState == EnemyState.Pursuing)
			{
				_currentTarget = null;
				ChangeState(EnemyState.Returning);
				GD.Print("Goblin: Player left territory, returning to patrol");
			}
		}
	}
}
