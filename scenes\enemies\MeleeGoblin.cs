using Godot;
using System;
using System.Collections.Generic;

/// <summary>
/// Melee Goblin enemy implementation
/// Territorial enemy that attacks with melee weapons when player enters territory
/// </summary>
public partial class MeleeGoblin : BaseEnemy
{
	[Export] public float ChargeSpeedMultiplier { get; set; } = 1.5f;
	[Export] public float AttackWindupTime { get; set; } = 0.8f;
	[Export] public ResourceType DropResourceType { get; set; } = ResourceType.RawRabbitLeg;
	[Export] public int DropAmount { get; set; } = 1;
	[Export] public float AttackArcAngle { get; set; } = 180.0f; // Attack arc in degrees
	[Export] public float AttackDistance { get; set; } = 32.0f; // Attack reach distance

	private float _attackStartTime = 0.0f;
	private bool _isCharging = false;
	private bool _isWindingUp = false;
	private Vector2 _attackTargetPosition = Vector2.Zero;

	public override void _Ready()
	{
		// Set goblin-specific properties
		EnemyType = EnemyType.Goblin;
		MaxHealth = EnemyType.Goblin.GetMaxHealth();
		AttackDamage = EnemyType.Goblin.GetAttackDamage();
		MovementSpeed = EnemyType.Goblin.GetMovementSpeed();
		DetectionRange = EnemyType.Goblin.GetDetectionRange();
		AttackRange = EnemyType.Goblin.GetAttackRange();
		XpReward = EnemyType.Goblin.GetXpReward();
		
		// Goblins are territorial but not initially aggressive
		_isAggressive = false;
		
		base._Ready();
	}

	protected override void HandlePursuit(double delta)
	{
		if (_currentTarget == null || !IsInstanceValid(_currentTarget))
		{
			ChangeState(EnemyState.Patrolling);
			return;
		}

		float distanceToTarget = GlobalPosition.DistanceTo(_currentTarget.GlobalPosition);
		
		// Start charging when close enough
		if (distanceToTarget <= AttackRange * 2.0f && !_isCharging)
		{
			_isCharging = true;
			PlayAnimation("move_down"); // Use movement animation for charging
		}

		// Check if target is in attack range
		if (distanceToTarget <= AttackRange)
		{
			_isCharging = false;
			ChangeState(EnemyState.Attacking);
			return;
		}

		// Check if we should return to territory
		if (ShouldReturnToTerritory())
		{
			_isCharging = false;
			ChangeState(EnemyState.Returning);
			return;
		}

		// Move towards target with charge speed if charging
		float currentSpeed = _isCharging ? MovementSpeed * ChargeSpeedMultiplier : MovementSpeed;
		MoveTowardsTargetWithSpeed(_currentTarget.GlobalPosition, currentSpeed);
	}

	protected override void HandleAttacking(double delta)
	{
		if (_currentTarget == null || !IsInstanceValid(_currentTarget))
		{
			ChangeState(EnemyState.Patrolling);
			return;
		}

		// Check if target is still in range
		float distanceToTarget = GlobalPosition.DistanceTo(_currentTarget.GlobalPosition);
		if (distanceToTarget > AttackRange)
		{
			ChangeState(EnemyState.Pursuing);
			return;
		}

		// Start attack windup if not already attacking
		if (!_isWindingUp && _attackTimer.IsStopped())
		{
			StartAttackWindup();
		}

		// Execute attack after windup
		if (_isWindingUp)
		{
			float currentTime = Time.GetTicksMsec() / 1000.0f;
			if (currentTime - _attackStartTime >= AttackWindupTime)
			{
				ExecuteAttack();
				_isWindingUp = false;
				_attackTimer.Start();
			}
		}
	}

	protected override void HandleReturning(double delta)
	{
		_isCharging = false;
		base.HandleReturning(delta);
	}

	protected override void OnStateChanged(EnemyState newState)
	{
		switch (newState)
		{
			case EnemyState.Patrolling:
				_isCharging = false;
				_isWindingUp = false;
				PlayAnimation("idle_" + _lastDirection);
				_stateTimer.WaitTime = PatrolInterval;
				_stateTimer.Start();
				break;
			case EnemyState.Pursuing:
				_isWindingUp = false;
				// Animation handled in movement
				break;
			case EnemyState.Attacking:
				// Animation handled in attack windup
				break;
			case EnemyState.Returning:
				_isCharging = false;
				_isWindingUp = false;
				// Animation handled in movement
				break;
			case EnemyState.Stunned:
				_isCharging = false;
				_isWindingUp = false;
				PlayAnimation("got_hit_" + _lastDirection);
				_stateTimer.WaitTime = StunDuration;
				_stateTimer.Start();
				break;
		}
	}

	private void StartAttackWindup()
	{
		_isWindingUp = true;
		_attackStartTime = Time.GetTicksMsec() / 1000.0f;

		// Store target position when attack starts (like player sword)
		if (_currentTarget != null)
		{
			_attackTargetPosition = _currentTarget.GlobalPosition;
			Vector2 directionToTarget = (_attackTargetPosition - GlobalPosition).Normalized();
			_lastDirection = GetDirectionString(directionToTarget);
		}

		PlayAnimation("attack_" + _lastDirection);
	}

	protected override void ExecuteAttack()
	{
		// Execute attack similar to player sword - 180 degree arc towards stored target position
		Vector2 attackDirection = (_attackTargetPosition - GlobalPosition).Normalized();

		// Find all potential targets in attack range
		var potentialTargets = new List<Node2D>();

		// Check for player
		var player = GetNode<PlayerController>("/root/world/Player");
		if (player != null && IsInAttackArc(player.GlobalPosition, attackDirection))
		{
			potentialTargets.Add(player);
		}

		// TODO: Add building detection when ICombatTarget is implemented

		bool hitSomething = false;

		// Apply damage to all targets in arc
		foreach (var target in potentialTargets)
		{
			if (target is PlayerController playerTarget)
			{
				ApplyAttackToPlayer(playerTarget);
				hitSomething = true;
			}
		}

		// Emit attack signal
		CommonSignals.Instance?.EmitEnemyAttack(GlobalPosition, AttackDamage, EnemyType);

		// Play attack sound/effects
		PlayAttackEffects();

		GD.Print($"Goblin attacked in direction {_lastDirection}! Hit something: {hitSomething}");
	}

	private bool IsInAttackArc(Vector2 targetPosition, Vector2 attackDirection)
	{
		float distance = GlobalPosition.DistanceTo(targetPosition);
		if (distance > AttackDistance) return false;

		Vector2 directionToTarget = (targetPosition - GlobalPosition).Normalized();
		float dotProduct = attackDirection.Dot(directionToTarget);

		// Convert arc angle to dot product threshold
		float arcRadians = AttackArcAngle * Mathf.Pi / 180.0f;
		float threshold = Mathf.Cos(arcRadians / 2.0f);

		return dotProduct >= threshold;
	}

	private void ApplyAttackToPlayer(PlayerController player)
	{
		// Apply damage to player (this will need to be implemented in PlayerController)
		if (player.HasMethod("TakeDamage"))
		{
			player.Call("TakeDamage", AttackDamage);
		}

		// Apply knockback to player
		Vector2 knockbackDirection = (player.GlobalPosition - GlobalPosition).Normalized();
		Vector2 knockbackTarget = player.GlobalPosition + knockbackDirection * 32.0f;
		
		// Create knockback tween for player
		var knockbackTween = CreateTween();
		knockbackTween.TweenProperty(player, "global_position", knockbackTarget, 0.3f);
		knockbackTween.SetEase(Tween.EaseType.Out);
		knockbackTween.SetTrans(Tween.TransitionType.Quart);
	}

	private void PlayAttackEffects()
	{
		// TODO: Add attack sound effects and visual effects
		// For now, just print debug info
		GD.Print($"Goblin attack effects at {GlobalPosition}");
	}

	protected override void UpdateFacingDirection(Vector2 direction)
	{
		// Update last direction for consistent facing
		_lastDirection = GetDirectionString(direction);

		// Play appropriate movement animation based on direction
		string animationName = "move_" + _lastDirection;
		PlayAnimation(animationName);
	}

	private void MoveTowardsTargetWithSpeed(Vector2 targetPos, float speed)
	{
		_startPosition = GlobalPosition;
		_targetPosition = targetPos;
		_movementProgress = 0.0f;
		_isMoving = true;

		// Update facing direction
		Vector2 direction = (targetPos - GlobalPosition).Normalized();
		UpdateFacingDirection(direction);

		// Override movement speed temporarily
		float originalSpeed = MovementSpeed;
		MovementSpeed = speed;
		
		// Restore original speed after movement
		GetTree().CreateTimer(0.1f).Timeout += () => {
			MovementSpeed = originalSpeed;
		};
	}

	protected override void DropResources()
	{
		// Goblins drop resources when defeated
		if (DropAmount > 0)
		{
			DroppedResource.SpawnResource(GlobalPosition, DropResourceType, DropAmount);
		}
	}

	protected override void CheckForTargets()
	{
		// Goblins are territorial - they only attack if player enters their territory
		// or if they were hit by the player
		if (!_wasHitByPlayer)
		{
			// Check if player is in territory
			var player = GetNode<PlayerController>("/root/world/Player");
			if (player != null)
			{
				float distanceFromTerritory = player.GlobalPosition.DistanceTo(_territoryCenter);
				if (distanceFromTerritory <= TerritoryRadius)
				{
					float distanceToPlayer = GlobalPosition.DistanceTo(player.GlobalPosition);
					if (distanceToPlayer <= DetectionRange)
					{
						_currentTarget = player;
						ChangeState(EnemyState.Pursuing);
						GD.Print($"Goblin detected player in territory! Distance: {distanceToPlayer}");
					}
				}
			}
		}
		else
		{
			// If hit by player, become aggressive and chase
			base.CheckForTargets();
		}
	}

	public override void TakeDamage(int damage)
	{
		base.TakeDamage(damage);
		
		// Goblins become aggressive when hit
		_isAggressive = true;
		_wasHitByPlayer = true;
		
		GD.Print($"Goblin took {damage} damage! Health: {_currentHealth}/{MaxHealth}");
	}

	// Territory management for Area2D approach
	public void SetTerritoryArea(Area2D territoryArea)
	{
		if (territoryArea != null)
		{
			// Connect to territory area signals for better territory management
			territoryArea.BodyEntered += OnTerritoryEntered;
			territoryArea.BodyExited += OnTerritoryExited;
		}
	}

	private void OnTerritoryEntered(Node2D body)
	{
		if (body is PlayerController player && !_wasHitByPlayer)
		{
			// Player entered territory - become alert
			if (_currentTarget == null)
			{
				_currentTarget = player;
				ChangeState(EnemyState.Pursuing);
				GD.Print("Goblin: Player entered territory!");
			}
		}
	}

	private void OnTerritoryExited(Node2D body)
	{
		if (body is PlayerController && !_wasHitByPlayer)
		{
			// Player left territory - return to patrolling if not aggressive
			if (!_isAggressive && _currentState == EnemyState.Pursuing)
			{
				_currentTarget = null;
				ChangeState(EnemyState.Returning);
				GD.Print("Goblin: Player left territory, returning to patrol");
			}
		}
	}
}
