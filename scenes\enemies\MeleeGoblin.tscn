[gd_scene load_steps=15 format=3 uid="uid://c8xvoaywqxhqy"]

[ext_resource type="Script" uid="uid://cri8gdcn42g6x" path="res://scenes/enemies/MeleeGoblin.cs" id="1_goblin_script"]
[ext_resource type="PackedScene" uid="uid://otpfc634hhga" path="res://scenes/UI/progress/ProgressBar.tscn" id="2_progress_bar"]
[ext_resource type="Texture2D" uid="uid://dg3x5ycl2xb0k" path="res://resources/pixel mood/enemies/Goblin.png" id="2_qjkrt"]

[sub_resource type="CapsuleShape2D" id="CapsuleShape2D_goblin_collision"]
radius = 6.0
height = 12.0

[sub_resource type="Animation" id="Animation_76opj"]
resource_name = "idle_up"
length = 0.4
loop_mode = 1
step = 0.1
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("Sprite2D:frame")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0, 0.1, 0.2),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 1,
"values": [0, 1, 2]
}

[sub_resource type="Animation" id="Animation_i5th4"]
length = 0.001
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("Sprite2D:frame")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [0]
}

[sub_resource type="Animation" id="Animation_qjkrt"]
resource_name = "idle_down"
length = 0.4
loop_mode = 1
step = 0.1
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("Sprite2D:frame")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0, 0.1, 0.2, 0.3),
"transitions": PackedFloat32Array(1, 1, 1, 1),
"update": 1,
"values": [4, 5, 6, 7]
}

[sub_resource type="Animation" id="Animation_ibskb"]
resource_name = "idle_left"
length = 0.4
loop_mode = 1
step = 0.1
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("Sprite2D:frame")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0, 0.1, 0.2, 0.3),
"transitions": PackedFloat32Array(1, 1, 1, 1),
"update": 1,
"values": [8, 9, 10, 11]
}

[sub_resource type="Animation" id="Animation_182s3"]
resource_name = "idle_right"
length = 0.4
loop_mode = 1
step = 0.1
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("Sprite2D:frame")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0, 0.1, 0.2, 0.3),
"transitions": PackedFloat32Array(1, 1, 1, 1),
"update": 1,
"values": [12, 13, 14, 15]
}

[sub_resource type="Animation" id="Animation_b0drt"]
resource_name = "move_up"
length = 0.4
loop_mode = 1
step = 0.1
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("Sprite2D:frame")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0, 0.1, 0.2, 0.3),
"transitions": PackedFloat32Array(1, 1, 1, 1),
"update": 1,
"values": [16, 17, 18, 19]
}

[sub_resource type="Animation" id="Animation_a4b6d"]
resource_name = "move_down"
length = 0.4
loop_mode = 1
step = 0.1
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("Sprite2D:frame")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0, 0.1, 0.2, 0.3),
"transitions": PackedFloat32Array(1, 1, 1, 1),
"update": 1,
"values": [20, 21, 22, 23]
}

[sub_resource type="Animation" id="Animation_vlfg2"]
resource_name = "move_left"
length = 0.4
loop_mode = 1
step = 0.1
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("Sprite2D:frame")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0, 0.1, 0.2, 0.3),
"transitions": PackedFloat32Array(1, 1, 1, 1),
"update": 1,
"values": [24, 25, 26, 27]
}

[sub_resource type="Animation" id="Animation_f51gl"]
resource_name = "move_right"
length = 0.4
loop_mode = 1
step = 0.1
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("Sprite2D:frame")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0, 0.1, 0.2, 0.3),
"transitions": PackedFloat32Array(1, 1, 1, 1),
"update": 1,
"values": [28, 29, 30, 31]
}

[sub_resource type="AnimationLibrary" id="AnimationLibrary_ibskb"]
_data = {
&"RESET": SubResource("Animation_i5th4"),
&"idle_down": SubResource("Animation_qjkrt"),
&"idle_left": SubResource("Animation_ibskb"),
&"idle_right": SubResource("Animation_182s3"),
&"idle_up": SubResource("Animation_76opj"),
&"move_down": SubResource("Animation_a4b6d"),
&"move_left": SubResource("Animation_vlfg2"),
&"move_right": SubResource("Animation_f51gl"),
&"move_up": SubResource("Animation_b0drt")
}

[node name="MeleeGoblin" type="CharacterBody2D"]
script = ExtResource("1_goblin_script")
EnemyType = 1
MaxHealth = 25
AttackDamage = 8
MovementSpeed = 35.0
DetectionRange = 80.0
AttackRange = 24.0
TerritoryRadius = 120.0
XpReward = 15
AttackCooldown = 2.0
PatrolInterval = 3.0
StunDuration = 0.5
ChargeSpeedMultiplier = 1.5
AttackWindupTime = 0.8
DropResourceType = 26
DropAmount = 1

[node name="Sprite2D" type="Sprite2D" parent="."]
texture_filter = 1
texture = ExtResource("2_qjkrt")
hframes = 4
vframes = 18

[node name="CollisionShape2D" type="CollisionShape2D" parent="."]
shape = SubResource("CapsuleShape2D_goblin_collision")

[node name="AnimationPlayer" type="AnimationPlayer" parent="."]
libraries = {
&"": SubResource("AnimationLibrary_ibskb")
}
speed_scale = 0.5

[node name="ProgressBar" parent="." instance=ExtResource("2_progress_bar")]
visible = false
position = Vector2(0, -16)
scale = Vector2(1, 0.6)
